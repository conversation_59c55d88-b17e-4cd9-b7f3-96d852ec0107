"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/chat-list */ \"(app-pages-browser)/./src/components/chat/chat-list.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/character-profile-sidebar */ \"(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPageContent() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatListOpen, setIsChatListOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = searchParams.get('id');\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatPageContent.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatPageContent.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatPageContent.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatPageContent.useEffect\"];\n        }\n    }[\"ChatPageContent.useEffect\"], []);\n    // Mobile behavior: close chat list when chat is selected, close profile by default\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (isMobile) {\n                if (selectedChat) {\n                    setIsChatListOpen(false);\n                }\n                setIsProfileOpen(false);\n            } else {\n                setIsChatListOpen(true);\n                setIsProfileOpen(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isMobile,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                // Show auth modal instead of redirecting\n                setShowAuthModal(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    // Handle chats loaded callback\n    const handleChatsLoaded = (chats)=>{\n        console.log('Chats loaded:', chats);\n        // Auto-select chat from URL if available\n        if (chatId && !selectedChat) {\n            const targetChat = chats.find((chat)=>chat.id === chatId);\n            if (targetChat) {\n                console.log('Auto-selecting chat from URL:', targetChat);\n                setSelectedChat(targetChat);\n            } else {\n                console.log('Chat not found in loaded chats:', chatId);\n                // Remove invalid chat ID from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('id');\n                window.history.replaceState({}, '', newUrl.toString());\n            }\n        }\n    };\n    const handleChatSelect = (chat)=>{\n        setSelectedChat(chat);\n        // Update URL with chat ID\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.set('id', chat.id);\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, close chat list when chat is selected\n        if (isMobile) {\n            setIsChatListOpen(false);\n        }\n    };\n    const handleBackToList = ()=>{\n        setSelectedChat(null);\n        // Remove chat ID from URL\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.delete('id');\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, show chat list when going back\n        if (isMobile) {\n            setIsChatListOpen(true);\n        }\n    };\n    // Toggle chat list (for mobile)\n    const toggleChatList = ()=>{\n        setIsChatListOpen(!isChatListOpen);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                    className: \"flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                        className: \"-ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    className: \"hidden md:block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                        href: \"/dashboard\",\n                                                        children: \"Bestieku\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                    className: \"hidden md:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                        children: \"Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center max-w-md mx-auto p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Selamat Datang di Bestieku Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-6\",\n                                            children: \"Untuk mulai mengobrol dengan karakter AI favorit Anda, silakan masuk atau daftar terlebih dahulu.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAuthModal(true),\n                                            className: \"inline-flex items-center px-6 py-3 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors font-medium\",\n                                            children: \"Masuk / Daftar\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                className: \"flex flex-col h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                    children: \"Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 min-h-0 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n            \".concat(isMobile ? \"absolute inset-y-0 left-0 z-20 w-full bg-background transform transition-transform duration-300 \".concat(isChatListOpen ? 'translate-x-0' : '-translate-x-full') : 'w-80 border-r bg-background', \"\\n            flex flex-col min-h-0\\n          \"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__.ChatList, {\n                                    selectedChatId: selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id,\n                                    onChatSelect: handleChatSelect,\n                                    onChatsLoaded: handleChatsLoaded\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n            \".concat(isMobile ? 'flex-1' : 'flex-1', \"\\n            flex min-h-0\\n          \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col min-h-0\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__.ChatInterface, {\n                                            chat: selectedChat,\n                                            onBack: handleBackToList\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 242,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center max-w-md mx-auto p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: \"Welcome to Bestieku Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6\",\n                                                        children: isMobile ? \"Tap the menu to select a chat or browse characters to start a new conversation.\" : \"Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: toggleChatList,\n                                                                className: \"inline-flex items-center px-4 py-2 bg-muted hover:bg-muted/80 text-foreground rounded-lg transition-colors w-full justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 263,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"View Chats\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 259,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push('/dashboard'),\n                                                                className: \"inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors w-full justify-center\",\n                                                                children: \"Browse Characters\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 267,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 257,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedChat && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 min-h-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: isProfileOpen,\n                                            onToggle: ()=>setIsProfileOpen(!isProfileOpen)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 282,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            isMobile && isChatListOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50 z-10\",\n                                onClick: ()=>setIsChatListOpen(false)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 294,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPageContent, \"/cpINkfDV5S577WC7EARMNrbbcc=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPageContent;\nfunction ChatPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 310,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 309,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 308,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatPageContent, {}, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 315,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 307,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChatPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPageContent\");\n$RefreshReg$(_c1, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVzRDtBQUNPO0FBRU47QUFDVTtBQUNxQjtBQUNwQztBQUNPO0FBQ0g7QUFRbEI7QUFDa0I7QUFLckI7QUFDa0I7QUFFbkQsU0FBU3NCOztJQUNQLE1BQU0sRUFBRUMsZUFBZSxFQUFFQyxTQUFTLEVBQUUsR0FBR2hCLCtEQUFPQTtJQUM5QyxNQUFNLENBQUNpQixjQUFjQyxnQkFBZ0IsR0FBRzFCLCtDQUFRQSxDQUFjO0lBQzlELE1BQU0sQ0FBQzJCLGVBQWVDLGlCQUFpQixHQUFHNUIsK0NBQVFBLENBQUM7SUFDbkQsTUFBTSxDQUFDNkIsZUFBZUMsaUJBQWlCLEdBQUc5QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUMrQixnQkFBZ0JDLGtCQUFrQixHQUFHaEMsK0NBQVFBLENBQUM7SUFDckQsTUFBTSxDQUFDaUMsVUFBVUMsWUFBWSxHQUFHbEMsK0NBQVFBLENBQUM7SUFDekMsTUFBTW1DLGVBQWVoQyxnRUFBZUE7SUFDcEMsTUFBTWlDLFNBQVNoQywwREFBU0E7SUFDeEIsTUFBTWlDLFNBQVNGLGFBQWFHLEdBQUcsQ0FBQztJQUVoQyw0QkFBNEI7SUFDNUJyQyxnREFBU0E7cUNBQUM7WUFDUixNQUFNc0M7eURBQWM7b0JBQ2xCTCxZQUFZTSxPQUFPQyxVQUFVLEdBQUc7Z0JBQ2xDOztZQUVBRjtZQUNBQyxPQUFPRSxnQkFBZ0IsQ0FBQyxVQUFVSDtZQUNsQzs2Q0FBTyxJQUFNQyxPQUFPRyxtQkFBbUIsQ0FBQyxVQUFVSjs7UUFDcEQ7b0NBQUcsRUFBRTtJQUVMLG1GQUFtRjtJQUNuRnRDLGdEQUFTQTtxQ0FBQztZQUNSLElBQUlnQyxVQUFVO2dCQUNaLElBQUlSLGNBQWM7b0JBQ2hCTyxrQkFBa0I7Z0JBQ3BCO2dCQUNBSixpQkFBaUI7WUFDbkIsT0FBTztnQkFDTEksa0JBQWtCO2dCQUNsQkosaUJBQWlCO1lBQ25CO1FBQ0Y7b0NBQUc7UUFBQ0s7UUFBVVI7S0FBYTtJQUUzQnhCLGdEQUFTQTtxQ0FBQztZQUNSLElBQUksQ0FBQ3VCLGFBQWEsQ0FBQ0QsaUJBQWlCO2dCQUNsQyx5Q0FBeUM7Z0JBQ3pDTyxpQkFBaUI7WUFDbkI7UUFDRjtvQ0FBRztRQUFDUDtRQUFpQkM7S0FBVTtJQUUvQiwrQkFBK0I7SUFDL0IsTUFBTW9CLG9CQUFvQixDQUFDQztRQUN6QkMsUUFBUUMsR0FBRyxDQUFDLGlCQUFpQkY7UUFFN0IseUNBQXlDO1FBQ3pDLElBQUlSLFVBQVUsQ0FBQ1osY0FBYztZQUMzQixNQUFNdUIsYUFBYUgsTUFBTUksSUFBSSxDQUFDQyxDQUFBQSxPQUFRQSxLQUFLQyxFQUFFLEtBQUtkO1lBQ2xELElBQUlXLFlBQVk7Z0JBQ2RGLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBaUNDO2dCQUM3Q3RCLGdCQUFnQnNCO1lBQ2xCLE9BQU87Z0JBQ0xGLFFBQVFDLEdBQUcsQ0FBQyxtQ0FBbUNWO2dCQUMvQyxrQ0FBa0M7Z0JBQ2xDLE1BQU1lLFNBQVMsSUFBSUMsSUFBSWIsT0FBT2MsUUFBUSxDQUFDQyxJQUFJO2dCQUMzQ0gsT0FBT2pCLFlBQVksQ0FBQ3FCLE1BQU0sQ0FBQztnQkFDM0JoQixPQUFPaUIsT0FBTyxDQUFDQyxZQUFZLENBQUMsQ0FBQyxHQUFHLElBQUlOLE9BQU9PLFFBQVE7WUFDckQ7UUFDRjtJQUNGO0lBRUEsTUFBTUMsbUJBQW1CLENBQUNWO1FBQ3hCeEIsZ0JBQWdCd0I7UUFDaEIsMEJBQTBCO1FBQzFCLE1BQU1FLFNBQVMsSUFBSUMsSUFBSWIsT0FBT2MsUUFBUSxDQUFDQyxJQUFJO1FBQzNDSCxPQUFPakIsWUFBWSxDQUFDMEIsR0FBRyxDQUFDLE1BQU1YLEtBQUtDLEVBQUU7UUFDckNYLE9BQU9pQixPQUFPLENBQUNLLFNBQVMsQ0FBQyxDQUFDLEdBQUcsSUFBSVYsT0FBT08sUUFBUTtRQUVoRCxtREFBbUQ7UUFDbkQsSUFBSTFCLFVBQVU7WUFDWkQsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxNQUFNK0IsbUJBQW1CO1FBQ3ZCckMsZ0JBQWdCO1FBQ2hCLDBCQUEwQjtRQUMxQixNQUFNMEIsU0FBUyxJQUFJQyxJQUFJYixPQUFPYyxRQUFRLENBQUNDLElBQUk7UUFDM0NILE9BQU9qQixZQUFZLENBQUNxQixNQUFNLENBQUM7UUFDM0JoQixPQUFPaUIsT0FBTyxDQUFDSyxTQUFTLENBQUMsQ0FBQyxHQUFHLElBQUlWLE9BQU9PLFFBQVE7UUFFaEQsNENBQTRDO1FBQzVDLElBQUkxQixVQUFVO1lBQ1pELGtCQUFrQjtRQUNwQjtJQUNGO0lBRUEsZ0NBQWdDO0lBQ2hDLE1BQU1nQyxpQkFBaUI7UUFDckJoQyxrQkFBa0IsQ0FBQ0Q7SUFDckI7SUFFQSxJQUFJUCxXQUFXO1FBQ2IscUJBQ0UsOERBQUN5QztZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7SUFJN0M7SUFFQSxJQUFJLENBQUMzQyxpQkFBaUI7UUFDcEIscUJBQ0UsOERBQUNKLG9FQUFlQTs7OEJBQ2QsOERBQUNULCtEQUFVQTs7Ozs7OEJBQ1gsOERBQUNRLGlFQUFZQTtvQkFBQ2dELFdBQVU7O3NDQUN0Qiw4REFBQ0U7NEJBQU9GLFdBQVU7c0NBQ2hCLDRFQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUM5QyxtRUFBY0E7d0NBQUM4QyxXQUFVOzs7Ozs7a0RBQzFCLDhEQUFDakQsZ0VBQVNBO3dDQUNSb0QsYUFBWTt3Q0FDWkgsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDdkQsaUVBQVVBO2tEQUNULDRFQUFDRyxxRUFBY0E7OzhEQUNiLDhEQUFDRixxRUFBY0E7b0RBQUNzRCxXQUFVOzhEQUN4Qiw0RUFBQ3JELHFFQUFjQTt3REFBQzBDLE1BQUs7a0VBQWE7Ozs7Ozs7Ozs7OzhEQUlwQyw4REFBQ3ZDLDBFQUFtQkE7b0RBQUNrRCxXQUFVOzs7Ozs7OERBQy9CLDhEQUFDdEQscUVBQWNBOzhEQUNiLDRFQUFDRyxxRUFBY0E7a0VBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FRMUIsOERBQUNrRDs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ2IsNEVBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzdDLDBGQUFhQTs0Q0FBQzZDLFdBQVU7Ozs7OztzREFDekIsOERBQUNJOzRDQUFHSixXQUFVO3NEQUE2Qjs7Ozs7O3NEQUMzQyw4REFBQ0M7NENBQUVELFdBQVU7c0RBQTZCOzs7Ozs7c0RBRzFDLDhEQUFDSzs0Q0FDQ0MsU0FBUyxJQUFNMUMsaUJBQWlCOzRDQUNoQ29DLFdBQVU7c0RBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU1QsOERBQUN6RCxrRUFBU0E7b0JBQ1JnRSxRQUFRNUM7b0JBQ1I2QyxTQUFTLElBQU01QyxpQkFBaUI7Ozs7Ozs7Ozs7OztJQUl4QztJQUVBLHFCQUNFLDhEQUFDWCxvRUFBZUE7OzBCQUNkLDhEQUFDVCwrREFBVUE7Ozs7OzBCQUNYLDhEQUFDUSxpRUFBWUE7Z0JBQUNnRCxXQUFVOztrQ0FDdEIsOERBQUNFO3dCQUFPRixXQUFVO2tDQUNoQiw0RUFBQ0Q7NEJBQUlDLFdBQVU7OzhDQUNiLDhEQUFDOUMsbUVBQWNBO29DQUFDOEMsV0FBVTs7Ozs7OzhDQUMxQiw4REFBQ2pELGdFQUFTQTtvQ0FDUm9ELGFBQVk7b0NBQ1pILFdBQVU7Ozs7Ozs4Q0FFWiw4REFBQ3ZELGlFQUFVQTs4Q0FDVCw0RUFBQ0cscUVBQWNBOzswREFDYiw4REFBQ0YscUVBQWNBO2dEQUFDc0QsV0FBVTswREFDeEIsNEVBQUNyRCxxRUFBY0E7b0RBQUMwQyxNQUFLOzhEQUFhOzs7Ozs7Ozs7OzswREFJcEMsOERBQUN2QywwRUFBbUJBO2dEQUFDa0QsV0FBVTs7Ozs7OzBEQUMvQiw4REFBQ3RELHFFQUFjQTswREFDYiw0RUFBQ0cscUVBQWNBOzhEQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBUTFCLDhEQUFDa0Q7d0JBQUlDLFdBQVU7OzBDQUViLDhEQUFDRDtnQ0FBSUMsV0FBVyxpQkFNYixPQUxDakMsV0FDRSxtR0FFQyxPQURDRixpQkFBaUIsa0JBQWtCLHVCQUVyQywrQkFDSDswQ0FHRCw0RUFBQzFCLGdFQUFRQTtvQ0FDUHNFLGNBQWMsRUFBRWxELHlCQUFBQSxtQ0FBQUEsYUFBYzBCLEVBQUU7b0NBQ2hDeUIsY0FBY2hCO29DQUNkaUIsZUFBZWpDOzs7Ozs7Ozs7OzswQ0FLbkIsOERBQUNxQjtnQ0FBSUMsV0FBVyxpQkFDbUIsT0FBL0JqQyxXQUFXLFdBQVcsVUFBUzs7a0RBR2pDLDhEQUFDZ0M7d0NBQUlDLFdBQVU7a0RBQ1p6Qyw2QkFDQyw4REFBQ25CLDBFQUFhQTs0Q0FDWjRDLE1BQU16Qjs0Q0FDTnFELFFBQVFmOzs7OztpRUFHViw4REFBQ0U7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzdDLDBGQUFhQTt3REFBQzZDLFdBQVU7Ozs7OztrRUFDekIsOERBQUNJO3dEQUFHSixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMzQyw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQ1ZqQyxXQUNHLG9GQUNBOzs7Ozs7a0VBR04sOERBQUNnQzt3REFBSUMsV0FBVTs7NERBQ1pqQywwQkFDQyw4REFBQ3NDO2dFQUNDQyxTQUFTUjtnRUFDVEUsV0FBVTs7a0ZBRVYsOERBQUM3QywwRkFBYUE7d0VBQUM2QyxXQUFVOzs7Ozs7b0VBQWlCOzs7Ozs7OzBFQUk5Qyw4REFBQ0s7Z0VBQ0NDLFNBQVMsSUFBTXBDLE9BQU8yQyxJQUFJLENBQUM7Z0VBQzNCYixXQUFVOzBFQUNYOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O29DQVVWekMsZ0JBQWdCLENBQUNRLDBCQUNoQiw4REFBQ2dDO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDM0QsK0ZBQXVCQTs0Q0FDdEJ5RSxhQUFhdkQsYUFBYXVELFdBQVc7NENBQ3JDQyxjQUFjeEQsYUFBYXdELFlBQVk7NENBQ3ZDUixRQUFROUM7NENBQ1J1RCxVQUFVLElBQU10RCxpQkFBaUIsQ0FBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7OzRCQU96Q00sWUFBWUYsZ0NBQ1gsOERBQUNrQztnQ0FDQ0MsV0FBVTtnQ0FDVk0sU0FBUyxJQUFNeEMsa0JBQWtCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPL0M7R0FuUlNWOztRQUNnQ2QsMkRBQU9BO1FBTXpCTCw0REFBZUE7UUFDckJDLHNEQUFTQTs7O0tBUmpCa0I7QUFxUk0sU0FBUzZEO0lBQ3RCLHFCQUNFLDhEQUFDakYsMkNBQVFBO1FBQUNrRix3QkFDUiw4REFBQ25CO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztrQkFJekMsNEVBQUM1Qzs7Ozs7Ozs7OztBQUdQO01BYndCNkQiLCJzb3VyY2VzIjpbIkU6XFxiZXNzdGlla3VcXGZyb250ZW5kXFxzcmNcXGFwcFxcY2hhdFxccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xyXG5cclxuaW1wb3J0IHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgU3VzcGVuc2UgfSBmcm9tICdyZWFjdCc7XHJcbmltcG9ydCB7IHVzZVNlYXJjaFBhcmFtcywgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJztcclxuaW1wb3J0IHsgQ2hhdCB9IGZyb20gJ0AvdHlwZXMvY2hhdCc7XHJcbmltcG9ydCB7IENoYXRMaXN0IH0gZnJvbSAnQC9jb21wb25lbnRzL2NoYXQvY2hhdC1saXN0JztcclxuaW1wb3J0IHsgQ2hhdEludGVyZmFjZSB9IGZyb20gJ0AvY29tcG9uZW50cy9jaGF0L2NoYXQtaW50ZXJmYWNlJztcclxuaW1wb3J0IHsgQ2hhcmFjdGVyUHJvZmlsZVNpZGViYXIgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9jaGFyYWN0ZXItcHJvZmlsZS1zaWRlYmFyJztcclxuaW1wb3J0IHsgdXNlQXV0aCB9IGZyb20gJ0AvY29udGV4dHMvYXV0aC1jb250ZXh0JztcclxuaW1wb3J0IHsgQXV0aE1vZGFsIH0gZnJvbSAnQC9jb21wb25lbnRzL2F1dGgvYXV0aC1tb2RhbCc7XHJcbmltcG9ydCB7IEFwcFNpZGViYXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL2FwcC1zaWRlYmFyXCI7XHJcbmltcG9ydCB7XHJcbiAgQnJlYWRjcnVtYixcclxuICBCcmVhZGNydW1iSXRlbSxcclxuICBCcmVhZGNydW1iTGluayxcclxuICBCcmVhZGNydW1iTGlzdCxcclxuICBCcmVhZGNydW1iUGFnZSxcclxuICBCcmVhZGNydW1iU2VwYXJhdG9yLFxyXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYnJlYWRjcnVtYlwiO1xyXG5pbXBvcnQgeyBTZXBhcmF0b3IgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL3NlcGFyYXRvclwiO1xyXG5pbXBvcnQge1xyXG4gIFNpZGViYXJJbnNldCxcclxuICBTaWRlYmFyUHJvdmlkZXIsXHJcbiAgU2lkZWJhclRyaWdnZXIsXHJcbn0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zaWRlYmFyXCI7XHJcbmltcG9ydCB7IE1lc3NhZ2VDaXJjbGUsIE1lbnUgfSBmcm9tICdsdWNpZGUtcmVhY3QnO1xyXG5cclxuZnVuY3Rpb24gQ2hhdFBhZ2VDb250ZW50KCkge1xyXG4gIGNvbnN0IHsgaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmcgfSA9IHVzZUF1dGgoKTtcclxuICBjb25zdCBbc2VsZWN0ZWRDaGF0LCBzZXRTZWxlY3RlZENoYXRdID0gdXNlU3RhdGU8Q2hhdCB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IFtpc1Byb2ZpbGVPcGVuLCBzZXRJc1Byb2ZpbGVPcGVuXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtzaG93QXV0aE1vZGFsLCBzZXRTaG93QXV0aE1vZGFsXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBbaXNDaGF0TGlzdE9wZW4sIHNldElzQ2hhdExpc3RPcGVuXSA9IHVzZVN0YXRlKHRydWUpO1xyXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IHNlYXJjaFBhcmFtcyA9IHVzZVNlYXJjaFBhcmFtcygpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG4gIGNvbnN0IGNoYXRJZCA9IHNlYXJjaFBhcmFtcy5nZXQoJ2lkJyk7XHJcblxyXG4gIC8vIERldGVjdCBtb2JpbGUgc2NyZWVuIHNpemVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgY2hlY2tNb2JpbGUgPSAoKSA9PiB7XHJcbiAgICAgIHNldElzTW9iaWxlKHdpbmRvdy5pbm5lcldpZHRoIDwgNzY4KTtcclxuICAgIH07XHJcblxyXG4gICAgY2hlY2tNb2JpbGUoKTtcclxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgICByZXR1cm4gKCkgPT4gd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ3Jlc2l6ZScsIGNoZWNrTW9iaWxlKTtcclxuICB9LCBbXSk7XHJcblxyXG4gIC8vIE1vYmlsZSBiZWhhdmlvcjogY2xvc2UgY2hhdCBsaXN0IHdoZW4gY2hhdCBpcyBzZWxlY3RlZCwgY2xvc2UgcHJvZmlsZSBieSBkZWZhdWx0XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChpc01vYmlsZSkge1xyXG4gICAgICBpZiAoc2VsZWN0ZWRDaGF0KSB7XHJcbiAgICAgICAgc2V0SXNDaGF0TGlzdE9wZW4oZmFsc2UpO1xyXG4gICAgICB9XHJcbiAgICAgIHNldElzUHJvZmlsZU9wZW4oZmFsc2UpO1xyXG4gICAgfSBlbHNlIHtcclxuICAgICAgc2V0SXNDaGF0TGlzdE9wZW4odHJ1ZSk7XHJcbiAgICAgIHNldElzUHJvZmlsZU9wZW4odHJ1ZSk7XHJcbiAgICB9XHJcbiAgfSwgW2lzTW9iaWxlLCBzZWxlY3RlZENoYXRdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmICghaXNMb2FkaW5nICYmICFpc0F1dGhlbnRpY2F0ZWQpIHtcclxuICAgICAgLy8gU2hvdyBhdXRoIG1vZGFsIGluc3RlYWQgb2YgcmVkaXJlY3RpbmdcclxuICAgICAgc2V0U2hvd0F1dGhNb2RhbCh0cnVlKTtcclxuICAgIH1cclxuICB9LCBbaXNBdXRoZW50aWNhdGVkLCBpc0xvYWRpbmddKTtcclxuXHJcbiAgLy8gSGFuZGxlIGNoYXRzIGxvYWRlZCBjYWxsYmFja1xyXG4gIGNvbnN0IGhhbmRsZUNoYXRzTG9hZGVkID0gKGNoYXRzOiBDaGF0W10pID0+IHtcclxuICAgIGNvbnNvbGUubG9nKCdDaGF0cyBsb2FkZWQ6JywgY2hhdHMpO1xyXG5cclxuICAgIC8vIEF1dG8tc2VsZWN0IGNoYXQgZnJvbSBVUkwgaWYgYXZhaWxhYmxlXHJcbiAgICBpZiAoY2hhdElkICYmICFzZWxlY3RlZENoYXQpIHtcclxuICAgICAgY29uc3QgdGFyZ2V0Q2hhdCA9IGNoYXRzLmZpbmQoY2hhdCA9PiBjaGF0LmlkID09PSBjaGF0SWQpO1xyXG4gICAgICBpZiAodGFyZ2V0Q2hhdCkge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRvLXNlbGVjdGluZyBjaGF0IGZyb20gVVJMOicsIHRhcmdldENoYXQpO1xyXG4gICAgICAgIHNldFNlbGVjdGVkQ2hhdCh0YXJnZXRDaGF0KTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQ2hhdCBub3QgZm91bmQgaW4gbG9hZGVkIGNoYXRzOicsIGNoYXRJZCk7XHJcbiAgICAgICAgLy8gUmVtb3ZlIGludmFsaWQgY2hhdCBJRCBmcm9tIFVSTFxyXG4gICAgICAgIGNvbnN0IG5ld1VybCA9IG5ldyBVUkwod2luZG93LmxvY2F0aW9uLmhyZWYpO1xyXG4gICAgICAgIG5ld1VybC5zZWFyY2hQYXJhbXMuZGVsZXRlKCdpZCcpO1xyXG4gICAgICAgIHdpbmRvdy5oaXN0b3J5LnJlcGxhY2VTdGF0ZSh7fSwgJycsIG5ld1VybC50b1N0cmluZygpKTtcclxuICAgICAgfVxyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUNoYXRTZWxlY3QgPSAoY2hhdDogQ2hhdCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRDaGF0KGNoYXQpO1xyXG4gICAgLy8gVXBkYXRlIFVSTCB3aXRoIGNoYXQgSURcclxuICAgIGNvbnN0IG5ld1VybCA9IG5ldyBVUkwod2luZG93LmxvY2F0aW9uLmhyZWYpO1xyXG4gICAgbmV3VXJsLnNlYXJjaFBhcmFtcy5zZXQoJ2lkJywgY2hhdC5pZCk7XHJcbiAgICB3aW5kb3cuaGlzdG9yeS5wdXNoU3RhdGUoe30sICcnLCBuZXdVcmwudG9TdHJpbmcoKSk7XHJcblxyXG4gICAgLy8gT24gbW9iaWxlLCBjbG9zZSBjaGF0IGxpc3Qgd2hlbiBjaGF0IGlzIHNlbGVjdGVkXHJcbiAgICBpZiAoaXNNb2JpbGUpIHtcclxuICAgICAgc2V0SXNDaGF0TGlzdE9wZW4oZmFsc2UpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIGNvbnN0IGhhbmRsZUJhY2tUb0xpc3QgPSAoKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENoYXQobnVsbCk7XHJcbiAgICAvLyBSZW1vdmUgY2hhdCBJRCBmcm9tIFVSTFxyXG4gICAgY29uc3QgbmV3VXJsID0gbmV3IFVSTCh3aW5kb3cubG9jYXRpb24uaHJlZik7XHJcbiAgICBuZXdVcmwuc2VhcmNoUGFyYW1zLmRlbGV0ZSgnaWQnKTtcclxuICAgIHdpbmRvdy5oaXN0b3J5LnB1c2hTdGF0ZSh7fSwgJycsIG5ld1VybC50b1N0cmluZygpKTtcclxuXHJcbiAgICAvLyBPbiBtb2JpbGUsIHNob3cgY2hhdCBsaXN0IHdoZW4gZ29pbmcgYmFja1xyXG4gICAgaWYgKGlzTW9iaWxlKSB7XHJcbiAgICAgIHNldElzQ2hhdExpc3RPcGVuKHRydWUpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIFRvZ2dsZSBjaGF0IGxpc3QgKGZvciBtb2JpbGUpXHJcbiAgY29uc3QgdG9nZ2xlQ2hhdExpc3QgPSAoKSA9PiB7XHJcbiAgICBzZXRJc0NoYXRMaXN0T3BlbighaXNDaGF0TGlzdE9wZW4pO1xyXG4gIH07XHJcblxyXG4gIGlmIChpc0xvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1bIzJERDRCRl0gbXgtYXV0byBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nLi4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgICk7XHJcbiAgfVxyXG5cclxuICBpZiAoIWlzQXV0aGVudGljYXRlZCkge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPFNpZGViYXJQcm92aWRlcj5cclxuICAgICAgICA8QXBwU2lkZWJhciAvPlxyXG4gICAgICAgIDxTaWRlYmFySW5zZXQgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICAgIDxoZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBoLTE2IHNocmluay0wIGl0ZW1zLWNlbnRlciBnYXAtMiBib3JkZXItYlwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTRcIj5cclxuICAgICAgICAgICAgICA8U2lkZWJhclRyaWdnZXIgY2xhc3NOYW1lPVwiLW1sLTFcIiAvPlxyXG4gICAgICAgICAgICAgIDxTZXBhcmF0b3JcclxuICAgICAgICAgICAgICAgIG9yaWVudGF0aW9uPVwidmVydGljYWxcIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMiBkYXRhLVtvcmllbnRhdGlvbj12ZXJ0aWNhbF06aC00XCJcclxuICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgIDxCcmVhZGNydW1iPlxyXG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaXN0PlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0gY2xhc3NOYW1lPVwiaGlkZGVuIG1kOmJsb2NrXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICBCZXN0aWVrdVxyXG4gICAgICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkxpbms+XHJcbiAgICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiIC8+XHJcbiAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iSXRlbT5cclxuICAgICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYlBhZ2U+Q2hhdDwvQnJlYWRjcnVtYlBhZ2U+XHJcbiAgICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJMaXN0PlxyXG4gICAgICAgICAgICAgIDwvQnJlYWRjcnVtYj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2hlYWRlcj5cclxuXHJcbiAgICAgICAgICB7LyogTWFpbiBDaGF0IEFyZWEgLSBVbmF1dGhlbnRpY2F0ZWQgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC0xIG1pbi1oLTBcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LW1kIG14LWF1dG8gcC04XCI+XHJcbiAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi02IHRleHQtbXV0ZWQtZm9yZWdyb3VuZC81MFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8aDIgY2xhc3NOYW1lPVwidGV4dC14bCBmb250LXNlbWlib2xkIG1iLTJcIj5TZWxhbWF0IERhdGFuZyBkaSBCZXN0aWVrdSBDaGF0PC9oMj5cclxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtbXV0ZWQtZm9yZWdyb3VuZCBtYi02XCI+XHJcbiAgICAgICAgICAgICAgICAgIFVudHVrIG11bGFpIG1lbmdvYnJvbCBkZW5nYW4ga2FyYWt0ZXIgQUkgZmF2b3JpdCBBbmRhLCBzaWxha2FuIG1hc3VrIGF0YXUgZGFmdGFyIHRlcmxlYmloIGRhaHVsdS5cclxuICAgICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gc2V0U2hvd0F1dGhNb2RhbCh0cnVlKX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTYgcHktMyBiZy1bIzJERDRCRl0gaG92ZXI6YmctWyMxNEI4QTZdIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyBmb250LW1lZGl1bVwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIE1hc3VrIC8gRGFmdGFyXHJcbiAgICAgICAgICAgICAgICA8L2J1dHRvbj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L1NpZGViYXJJbnNldD5cclxuXHJcbiAgICAgICAgey8qIEF1dGggTW9kYWwgKi99XHJcbiAgICAgICAgPEF1dGhNb2RhbFxyXG4gICAgICAgICAgaXNPcGVuPXtzaG93QXV0aE1vZGFsfVxyXG4gICAgICAgICAgb25DbG9zZT17KCkgPT4gc2V0U2hvd0F1dGhNb2RhbChmYWxzZSl9XHJcbiAgICAgICAgLz5cclxuICAgICAgPC9TaWRlYmFyUHJvdmlkZXI+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxTaWRlYmFyUHJvdmlkZXI+XHJcbiAgICAgIDxBcHBTaWRlYmFyIC8+XHJcbiAgICAgIDxTaWRlYmFySW5zZXQgY2xhc3NOYW1lPVwiZmxleCBmbGV4LWNvbCBoLXNjcmVlbiBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImZsZXggaC0xNiBzaHJpbmstMCBpdGVtcy1jZW50ZXIgZ2FwLTIgYm9yZGVyLWJcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTIgcHgtNFwiPlxyXG4gICAgICAgICAgICA8U2lkZWJhclRyaWdnZXIgY2xhc3NOYW1lPVwiLW1sLTFcIiAvPlxyXG4gICAgICAgICAgICA8U2VwYXJhdG9yXHJcbiAgICAgICAgICAgICAgb3JpZW50YXRpb249XCJ2ZXJ0aWNhbFwiXHJcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwibXItMiBkYXRhLVtvcmllbnRhdGlvbj12ZXJ0aWNhbF06aC00XCJcclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPEJyZWFkY3J1bWI+XHJcbiAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaXN0PlxyXG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkxpbmsgaHJlZj1cIi9kYXNoYm9hcmRcIj5cclxuICAgICAgICAgICAgICAgICAgICBCZXN0aWVrdVxyXG4gICAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJMaW5rPlxyXG4gICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iSXRlbT5cclxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iU2VwYXJhdG9yIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiIC8+XHJcbiAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iUGFnZT5DaGF0PC9CcmVhZGNydW1iUGFnZT5cclxuICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgPC9CcmVhZGNydW1iTGlzdD5cclxuICAgICAgICAgICAgPC9CcmVhZGNydW1iPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9oZWFkZXI+XHJcblxyXG4gICAgICAgIHsvKiBNYWluIENoYXQgQXJlYSAtIFJlc3BvbnNpdmUgbGF5b3V0ICovfVxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBmbGV4LTEgbWluLWgtMCByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgey8qIENoYXQgTGlzdCBTaWRlYmFyIC0gUmVzcG9uc2l2ZSB3aWR0aCBhbmQgcG9zaXRpb25pbmcgKi99XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT17YFxyXG4gICAgICAgICAgICAke2lzTW9iaWxlXHJcbiAgICAgICAgICAgICAgPyBgYWJzb2x1dGUgaW5zZXQteS0wIGxlZnQtMCB6LTIwIHctZnVsbCBiZy1iYWNrZ3JvdW5kIHRyYW5zZm9ybSB0cmFuc2l0aW9uLXRyYW5zZm9ybSBkdXJhdGlvbi0zMDAgJHtcclxuICAgICAgICAgICAgICAgICAgaXNDaGF0TGlzdE9wZW4gPyAndHJhbnNsYXRlLXgtMCcgOiAnLXRyYW5zbGF0ZS14LWZ1bGwnXHJcbiAgICAgICAgICAgICAgICB9YFxyXG4gICAgICAgICAgICAgIDogJ3ctODAgYm9yZGVyLXIgYmctYmFja2dyb3VuZCdcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICBmbGV4IGZsZXgtY29sIG1pbi1oLTBcclxuICAgICAgICAgIGB9PlxyXG4gICAgICAgICAgICA8Q2hhdExpc3RcclxuICAgICAgICAgICAgICBzZWxlY3RlZENoYXRJZD17c2VsZWN0ZWRDaGF0Py5pZH1cclxuICAgICAgICAgICAgICBvbkNoYXRTZWxlY3Q9e2hhbmRsZUNoYXRTZWxlY3R9XHJcbiAgICAgICAgICAgICAgb25DaGF0c0xvYWRlZD17aGFuZGxlQ2hhdHNMb2FkZWR9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogQ2hhdCBJbnRlcmZhY2UgLSBSZXNwb25zaXZlIGxheW91dCAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgXHJcbiAgICAgICAgICAgICR7aXNNb2JpbGUgPyAnZmxleC0xJyA6ICdmbGV4LTEnfVxyXG4gICAgICAgICAgICBmbGV4IG1pbi1oLTBcclxuICAgICAgICAgIGB9PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGZsZXgtY29sIG1pbi1oLTBcIj5cclxuICAgICAgICAgICAgICB7c2VsZWN0ZWRDaGF0ID8gKFxyXG4gICAgICAgICAgICAgICAgPENoYXRJbnRlcmZhY2VcclxuICAgICAgICAgICAgICAgICAgY2hhdD17c2VsZWN0ZWRDaGF0fVxyXG4gICAgICAgICAgICAgICAgICBvbkJhY2s9e2hhbmRsZUJhY2tUb0xpc3R9XHJcbiAgICAgICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICAgICkgOiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LW1kIG14LWF1dG8gcC04XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwidy0xNiBoLTE2IG14LWF1dG8gbWItNiB0ZXh0LW11dGVkLWZvcmVncm91bmQvNTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgbWItMlwiPldlbGNvbWUgdG8gQmVzdGlla3UgQ2hhdDwvaDI+XHJcbiAgICAgICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kIG1iLTZcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIHtpc01vYmlsZVxyXG4gICAgICAgICAgICAgICAgICAgICAgICA/IFwiVGFwIHRoZSBtZW51IHRvIHNlbGVjdCBhIGNoYXQgb3IgYnJvd3NlIGNoYXJhY3RlcnMgdG8gc3RhcnQgYSBuZXcgY29udmVyc2F0aW9uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogXCJTZWxlY3QgYSBjaGF0IGZyb20gdGhlIHNpZGViYXIgdG8gc3RhcnQgbWVzc2FnaW5nIHdpdGggeW91ciBBSSBjaGFyYWN0ZXJzLCBvciBnbyBiYWNrIHRvIHRoZSBkYXNoYm9hcmQgdG8gc3RhcnQgYSBuZXcgY29udmVyc2F0aW9uLlwiXHJcbiAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7aXNNb2JpbGUgJiYgKFxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17dG9nZ2xlQ2hhdExpc3R9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1tdXRlZCBob3ZlcjpiZy1tdXRlZC84MCB0ZXh0LWZvcmVncm91bmQgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwganVzdGlmeS1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBWaWV3IENoYXRzXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2goJy9kYXNoYm9hcmQnKX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaW5saW5lLWZsZXggaXRlbXMtY2VudGVyIHB4LTQgcHktMiBiZy1bIzJERDRCRl0gaG92ZXI6YmctWyMxNEI4QTZdIHRleHQtd2hpdGUgcm91bmRlZC1sZyB0cmFuc2l0aW9uLWNvbG9ycyB3LWZ1bGwganVzdGlmeS1jZW50ZXJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBCcm93c2UgQ2hhcmFjdGVyc1xyXG4gICAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgICAgICB7LyogQ2hhcmFjdGVyIFByb2ZpbGUgU2lkZWJhciAtIEhpZGRlbiBvbiBtb2JpbGUgYnkgZGVmYXVsdCAqL31cclxuICAgICAgICAgICAge3NlbGVjdGVkQ2hhdCAmJiAhaXNNb2JpbGUgJiYgKFxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBtaW4taC0wXCI+XHJcbiAgICAgICAgICAgICAgICA8Q2hhcmFjdGVyUHJvZmlsZVNpZGViYXJcclxuICAgICAgICAgICAgICAgICAgY2hhcmFjdGVySWQ9e3NlbGVjdGVkQ2hhdC5jaGFyYWN0ZXJJZH1cclxuICAgICAgICAgICAgICAgICAgbWVzc2FnZUNvdW50PXtzZWxlY3RlZENoYXQubWVzc2FnZUNvdW50fVxyXG4gICAgICAgICAgICAgICAgICBpc09wZW49e2lzUHJvZmlsZU9wZW59XHJcbiAgICAgICAgICAgICAgICAgIG9uVG9nZ2xlPXsoKSA9PiBzZXRJc1Byb2ZpbGVPcGVuKCFpc1Byb2ZpbGVPcGVuKX1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICB7LyogTW9iaWxlIG92ZXJsYXkgd2hlbiBjaGF0IGxpc3QgaXMgb3BlbiAqL31cclxuICAgICAgICAgIHtpc01vYmlsZSAmJiBpc0NoYXRMaXN0T3BlbiAmJiAoXHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIGJnLWJsYWNrLzUwIHotMTBcIlxyXG4gICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldElzQ2hhdExpc3RPcGVuKGZhbHNlKX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvU2lkZWJhckluc2V0PlxyXG4gICAgPC9TaWRlYmFyUHJvdmlkZXI+XHJcbiAgKTtcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQ2hhdFBhZ2UoKSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxTdXNwZW5zZSBmYWxsYmFjaz17XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC1zY3JlZW4gZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1bIzJERDRCRl0gbXgtYXV0byBtYi00XCI+PC9kaXY+XHJcbiAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5Mb2FkaW5nIGNoYXQuLi48L3A+XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgfT5cclxuICAgICAgPENoYXRQYWdlQ29udGVudCAvPlxyXG4gICAgPC9TdXNwZW5zZT5cclxuICApO1xyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsIlN1c3BlbnNlIiwidXNlU2VhcmNoUGFyYW1zIiwidXNlUm91dGVyIiwiQ2hhdExpc3QiLCJDaGF0SW50ZXJmYWNlIiwiQ2hhcmFjdGVyUHJvZmlsZVNpZGViYXIiLCJ1c2VBdXRoIiwiQXV0aE1vZGFsIiwiQXBwU2lkZWJhciIsIkJyZWFkY3J1bWIiLCJCcmVhZGNydW1iSXRlbSIsIkJyZWFkY3J1bWJMaW5rIiwiQnJlYWRjcnVtYkxpc3QiLCJCcmVhZGNydW1iUGFnZSIsIkJyZWFkY3J1bWJTZXBhcmF0b3IiLCJTZXBhcmF0b3IiLCJTaWRlYmFySW5zZXQiLCJTaWRlYmFyUHJvdmlkZXIiLCJTaWRlYmFyVHJpZ2dlciIsIk1lc3NhZ2VDaXJjbGUiLCJDaGF0UGFnZUNvbnRlbnQiLCJpc0F1dGhlbnRpY2F0ZWQiLCJpc0xvYWRpbmciLCJzZWxlY3RlZENoYXQiLCJzZXRTZWxlY3RlZENoYXQiLCJpc1Byb2ZpbGVPcGVuIiwic2V0SXNQcm9maWxlT3BlbiIsInNob3dBdXRoTW9kYWwiLCJzZXRTaG93QXV0aE1vZGFsIiwiaXNDaGF0TGlzdE9wZW4iLCJzZXRJc0NoYXRMaXN0T3BlbiIsImlzTW9iaWxlIiwic2V0SXNNb2JpbGUiLCJzZWFyY2hQYXJhbXMiLCJyb3V0ZXIiLCJjaGF0SWQiLCJnZXQiLCJjaGVja01vYmlsZSIsIndpbmRvdyIsImlubmVyV2lkdGgiLCJhZGRFdmVudExpc3RlbmVyIiwicmVtb3ZlRXZlbnRMaXN0ZW5lciIsImhhbmRsZUNoYXRzTG9hZGVkIiwiY2hhdHMiLCJjb25zb2xlIiwibG9nIiwidGFyZ2V0Q2hhdCIsImZpbmQiLCJjaGF0IiwiaWQiLCJuZXdVcmwiLCJVUkwiLCJsb2NhdGlvbiIsImhyZWYiLCJkZWxldGUiLCJoaXN0b3J5IiwicmVwbGFjZVN0YXRlIiwidG9TdHJpbmciLCJoYW5kbGVDaGF0U2VsZWN0Iiwic2V0IiwicHVzaFN0YXRlIiwiaGFuZGxlQmFja1RvTGlzdCIsInRvZ2dsZUNoYXRMaXN0IiwiZGl2IiwiY2xhc3NOYW1lIiwicCIsImhlYWRlciIsIm9yaWVudGF0aW9uIiwiaDIiLCJidXR0b24iLCJvbkNsaWNrIiwiaXNPcGVuIiwib25DbG9zZSIsInNlbGVjdGVkQ2hhdElkIiwib25DaGF0U2VsZWN0Iiwib25DaGF0c0xvYWRlZCIsIm9uQmFjayIsInB1c2giLCJjaGFyYWN0ZXJJZCIsIm1lc3NhZ2VDb3VudCIsIm9uVG9nZ2xlIiwiQ2hhdFBhZ2UiLCJmYWxsYmFjayJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});
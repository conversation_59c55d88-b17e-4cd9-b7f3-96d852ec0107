"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Info,MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 28,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 43,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 60,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 34,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 118,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c1 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { streamingMessage, characterImage, characterName } = param;\n    var _characterName_charAt;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 153,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Thinking\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 166,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Typing...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 175,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 174,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 160,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = StreamingMessage;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 237,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 242,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 223,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[#2DD4BF] animate-pulse\",\n                        children: \"AI is responding...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 255,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 222,\n        columnNumber: 5\n    }, undefined);\n}, \"1o5B5G/jtGrG8z8Ow45dQHalZdE=\"));\n_c3 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s2((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c4 = _s2((param)=>{\n    let { chat, onBack, onToggleProfile } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt, _chat_character_name, _chat_character6, _chat_character7;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatInterface.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatInterface.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatInterface.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], []);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Cleanup stream timeout\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({\n        \"ChatInterface.useRef[scrollToBottomRef]\": ()=>{}\n    }[\"ChatInterface.useRef[scrollToBottomRef]\"]);\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection and clear timeouts\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        if (streamingTimeoutRef.current) {\n            clearTimeout(streamingTimeoutRef.current);\n        }\n        if (streamTimeoutRef.current) {\n            clearTimeout(streamTimeoutRef.current);\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Clear stream timeout to prevent false timeout errors\n                    if (streamTimeoutRef.current) {\n                        clearTimeout(streamTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            var _error_message;\n            console.error('Stream Error:', error);\n            // Clear timeouts first to prevent further errors\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            if (streamTimeoutRef.current) {\n                clearTimeout(streamTimeoutRef.current);\n            }\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Only reload messages if it's not a timeout error and streaming was actually active\n            if (!((_error_message = error.message) === null || _error_message === void 0 ? void 0 : _error_message.includes('timeout')) && isStreaming) {\n                console.log('Attempting to reload messages as fallback...');\n                try {\n                    await loadMessages();\n                } catch (reloadError) {\n                    console.error('Failed to reload messages:', reloadError);\n                // Don't show alert for timeout errors to avoid disrupting user\n                }\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds) - but store reference to clear it\n            streamTimeoutRef.current = setTimeout(()=>{\n                // Only trigger timeout if streaming is still active and connection exists\n                if (isStreaming && streamConnectionRef.current) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 549,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 551,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 552,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 550,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 548,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 559,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 561,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 562,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 560,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 558,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 556,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 546,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                isMobile && onBack && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: onBack,\n                                    className: \"hover:bg-muted/50 md:hidden\",\n                                    \"aria-label\": \"Back to chat list\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-5 h-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 586,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 579,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-3 \".concat(isMobile && onToggleProfile ? 'cursor-pointer hover:bg-muted/50 rounded-lg p-2 -m-2 transition-colors' : ''),\n                                    onClick: isMobile && onToggleProfile ? onToggleProfile : undefined,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                                    className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                            src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                                            alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 598,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                            className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                            children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name = _chat_character6.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 599,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 597,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 604,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 596,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                    className: \"font-semibold text-lg\",\n                                                    children: ((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'Unknown Character'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                chat.messageCount,\n                                                                \" messages\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"•\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 610,\n                                                            columnNumber: 19\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-green-500\",\n                                                            children: \"Online\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 611,\n                                                            columnNumber: 19\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 606,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 576,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                isMobile && onToggleProfile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    onClick: onToggleProfile,\n                                    className: \"hover:bg-muted/50 md:hidden\",\n                                    \"aria-label\": \"View character profile\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 626,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 619,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"hover:bg-muted/50\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Info_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"w-4 h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 631,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 630,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 575,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 574,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                            message: message,\n                            characterImage: characterImageRef.current,\n                            characterName: characterNameRef.current\n                        }, message.id, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 640,\n                            columnNumber: 11\n                        }, undefined)),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                        streamingMessage: streamingMessage,\n                        characterImage: characterImageRef.current,\n                        characterName: characterNameRef.current\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 650,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 638,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: characterNameRef.current,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 661,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 572,\n        columnNumber: 5\n    }, undefined);\n}, \"DAhJda//Rph3zLp7xwa9IvKUsKM=\")), \"DAhJda//Rph3zLp7xwa9IvKUsKM=\");\n_c5 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem\");\n$RefreshReg$(_c2, \"StreamingMessage\");\n$RefreshReg$(_c3, \"ChatInput\");\n$RefreshReg$(_c4, \"ChatInterface$memo\");\n$RefreshReg$(_c5, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2NoYXQvY2hhdC1pbnRlcmZhY2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRThFO0FBRWhDO0FBQ0U7QUFDRjtBQUMrQjtBQUNWO0FBQ3BCO0FBQ0o7QUFDUjtBQWFuQyxNQUFNbUIsK0JBQWlCZCwyQ0FBSUEsQ0FBQztRQUFDLEVBQUVlLE9BQU8sRUFBRUMsSUFBSSxFQUF1QjtJQUNqRSxJQUFJQSxTQUFTLFFBQVE7UUFDbkIsaUVBQWlFO1FBQ2pFLHFCQUFPLDhEQUFDQztZQUFFQyxXQUFVO3NCQUErQ0g7Ozs7OztJQUNyRTtJQUVBLHNDQUFzQztJQUN0QyxxQkFDRSw4REFBQ0k7UUFBSUQsV0FBVTtrQkFDYiw0RUFBQ04sb0RBQWFBO1lBQ1pRLGVBQWU7Z0JBQUNQLGtEQUFTQTthQUFDO1lBQzFCUSxZQUFZO2dCQUNWLDhCQUE4QjtnQkFDOUJKLEdBQUc7d0JBQUMsRUFBRUssUUFBUSxFQUFFO3lDQUNkLDhEQUFDTDt3QkFBRUMsV0FBVTtrQ0FBa0NJOzs7Ozs7O2dCQUVqRCw4QkFBOEI7Z0JBQzlCQyxJQUFJO3dCQUFDLEVBQUVELFFBQVEsRUFBRTt5Q0FDZiw4REFBQ0M7d0JBQUdMLFdBQVU7a0NBQXVCSTs7Ozs7OztnQkFFdkMsMEJBQTBCO2dCQUMxQkUsUUFBUTt3QkFBQyxFQUFFRixRQUFRLEVBQUU7eUNBQ25CLDhEQUFDRTt3QkFBT04sV0FBVTtrQ0FBOEJJOzs7Ozs7O2dCQUVsRCx3QkFBd0I7Z0JBQ3hCRyxNQUFNO3dCQUFDLEVBQUVILFFBQVEsRUFBRUosU0FBUyxFQUFFO29CQUM1QixNQUFNUSxXQUFXLENBQUNSO29CQUNsQixJQUFJUSxVQUFVO3dCQUNaLHFCQUNFLDhEQUFDRDs0QkFBS1AsV0FBVTtzQ0FDYkk7Ozs7OztvQkFHUDtvQkFDQSxxQkFDRSw4REFBQ0c7d0JBQUtQLFdBQVU7a0NBQ2JJOzs7Ozs7Z0JBR1A7Z0JBQ0Esa0JBQWtCO2dCQUNsQkssSUFBSTt3QkFBQyxFQUFFTCxRQUFRLEVBQUU7eUNBQ2YsOERBQUNLO3dCQUFHVCxXQUFVO2tDQUF3Q0k7Ozs7Ozs7Z0JBRXhETSxJQUFJO3dCQUFDLEVBQUVOLFFBQVEsRUFBRTt5Q0FDZiw4REFBQ007d0JBQUdWLFdBQVU7a0NBQTJDSTs7Ozs7OztnQkFFM0RPLElBQUk7d0JBQUMsRUFBRVAsUUFBUSxFQUFFO3lDQUNmLDhEQUFDTzt3QkFBR1gsV0FBVTtrQ0FBV0k7Ozs7Ozs7Z0JBRTNCLHdCQUF3QjtnQkFDeEJRLFlBQVk7d0JBQUMsRUFBRVIsUUFBUSxFQUFFO3lDQUN2Qiw4REFBQ1E7d0JBQVdaLFdBQVU7a0NBQ25CSTs7Ozs7OztZQUdQO3NCQUVDUDs7Ozs7Ozs7Ozs7QUFJVDtLQTlETUQ7QUFzRU4sTUFBTWlCLDRCQUFjL0IsMkNBQUlBLElBQUM7UUFBQyxFQUFFZ0MsT0FBTyxFQUFFQyxjQUFjLEVBQUVDLGFBQWEsRUFBb0I7UUFrQnpFQTs7SUFqQlgsTUFBTUMsYUFBYXBDLGtEQUFXQTsrQ0FBQyxDQUFDcUM7WUFDOUIsSUFBSTtnQkFDRixPQUFPekIsd0dBQW1CQSxDQUFDLElBQUkwQixLQUFLRCxhQUFhO29CQUFFRSxXQUFXO2dCQUFLO1lBQ3JFLEVBQUUsVUFBTTtnQkFDTixPQUFPO1lBQ1Q7UUFDRjs4Q0FBRyxFQUFFO0lBRUwscUJBQ0UsOERBQUNuQjtRQUNDRCxXQUFXLHdCQUFrRixPQUExRGMsUUFBUWhCLElBQUksS0FBSyxTQUFTLGdCQUFnQjs7WUFHNUVnQixRQUFRaEIsSUFBSSxLQUFLLDZCQUNoQiw4REFBQ1oseURBQU1BO2dCQUFDYyxXQUFVOztrQ0FDaEIsOERBQUNaLDhEQUFXQTt3QkFBQ2lDLEtBQUtOO3dCQUFnQk8sS0FBS047Ozs7OztrQ0FDdkMsOERBQUM3QixpRUFBY0E7d0JBQUNhLFdBQVU7a0NBQ3ZCZ0IsQ0FBQUEsMEJBQUFBLHFDQUFBQSx3QkFBQUEsY0FBZU8sTUFBTSxDQUFDLGdCQUF0QlAsNENBQUFBLHNCQUEwQlEsV0FBVyxPQUFNOzs7Ozs7Ozs7Ozs7MEJBS2xELDhEQUFDdkI7Z0JBQ0NELFdBQVcseUNBSVYsT0FIQ2MsUUFBUWhCLElBQUksS0FBSyxTQUNiLDBDQUNBOztrQ0FHTiw4REFBQ0Y7d0JBQWVDLFNBQVNpQixRQUFRakIsT0FBTzt3QkFBRUMsTUFBTWdCLFFBQVFoQixJQUFJOzs7Ozs7a0NBQzVELDhEQUFDRzt3QkFBSUQsV0FBVywwQ0FFZixPQURDYyxRQUFRaEIsSUFBSSxLQUFLLFNBQVMsa0JBQWtCOzswQ0FFNUMsOERBQUNDO2dDQUFFQyxXQUFVOzBDQUNWaUIsV0FBV0gsUUFBUVcsU0FBUzs7Ozs7OzRCQUU5QlgsUUFBUWhCLElBQUksS0FBSyx3QkFDaEIsOERBQUNHO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFDZiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU83QjtNQS9DTWE7QUF1RE4sTUFBTWEsaUNBQW1CNUMsMkNBQUlBLENBQUM7UUFBQyxFQUFFNkMsZ0JBQWdCLEVBQUVaLGNBQWMsRUFBRUMsYUFBYSxFQUF5QjtRQU05RkE7SUFMVCxxQkFDRSw4REFBQ2Y7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNkLHlEQUFNQTtnQkFBQ2MsV0FBVTs7a0NBQ2hCLDhEQUFDWiw4REFBV0E7d0JBQUNpQyxLQUFLTjt3QkFBZ0JPLEtBQUtOOzs7Ozs7a0NBQ3ZDLDhEQUFDN0IsaUVBQWNBO3dCQUFDYSxXQUFVO2tDQUN2QmdCLENBQUFBLDBCQUFBQSxxQ0FBQUEsd0JBQUFBLGNBQWVPLE1BQU0sQ0FBQyxnQkFBdEJQLDRDQUFBQSxzQkFBMEJRLFdBQVcsT0FBTTs7Ozs7Ozs7Ozs7OzBCQUloRCw4REFBQ3ZCO2dCQUFJRCxXQUFVOztvQkFDWjJCLGlDQUNDLDhEQUFDL0I7d0JBQWVDLFNBQVM4Qjt3QkFBa0I3QixNQUFLOzs7OztrREFFaEQsOERBQUNHO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQzRCO2dDQUFLNUIsV0FBVTswQ0FBZ0M7Ozs7OzswQ0FDaEQsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDYiw4REFBQ0M7d0NBQUlELFdBQVU7Ozs7OztrREFDZiw4REFBQ0M7d0NBQUlELFdBQVU7d0NBQW1ENkIsT0FBTzs0Q0FBRUMsZ0JBQWdCO3dDQUFPOzs7Ozs7a0RBQ2xHLDhEQUFDN0I7d0NBQUlELFdBQVU7d0NBQW1ENkIsT0FBTzs0Q0FBRUMsZ0JBQWdCO3dDQUFPOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBS3hHLDhEQUFDN0I7d0JBQUlELFdBQVU7a0NBQ2IsNEVBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDYiw4REFBQ0M7b0NBQUlELFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQzRCO29DQUFLNUIsV0FBVTs4Q0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFNdEM7TUFqQ00wQjtBQTBDTixNQUFNSywwQkFBWWpELDJDQUFJQSxLQUFDO1FBQUMsRUFBRWtELGFBQWEsRUFBRUMsUUFBUSxFQUFFakIsYUFBYSxFQUFFa0IsV0FBVyxFQUFrQjs7SUFDN0YsTUFBTSxDQUFDQyxZQUFZQyxjQUFjLEdBQUcxRCwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNMkQsV0FBV3pELDZDQUFNQSxDQUFtQjtJQUUxQywyREFBMkQ7SUFDM0QsTUFBTTBELGFBQWExRCw2Q0FBTUEsQ0FBQztJQUMxQjBELFdBQVdDLE9BQU8sR0FBR0o7SUFFckIsTUFBTUssb0JBQW9CM0Qsa0RBQVdBO29EQUFDO1lBQ3BDLE1BQU00RCxpQkFBaUJILFdBQVdDLE9BQU8sQ0FBQ0csSUFBSTtZQUM5QyxJQUFJLENBQUNELGtCQUFrQlIsVUFBVTtZQUNqQ0QsY0FBY1M7WUFDZEwsY0FBYztZQUNkRSxXQUFXQyxPQUFPLEdBQUc7UUFDdkI7bURBQUc7UUFBQ047UUFBVUQ7S0FBYztJQUU1QixNQUFNVyxpQkFBaUI5RCxrREFBV0E7aURBQUMsQ0FBQytEO1lBQ2xDLElBQUlBLEVBQUVDLEdBQUcsS0FBSyxXQUFXLENBQUNELEVBQUVFLFFBQVEsRUFBRTtnQkFDcENGLEVBQUVHLGNBQWM7Z0JBQ2hCUDtZQUNGO1FBQ0Y7Z0RBQUc7UUFBQ0E7S0FBa0I7SUFFdEIsTUFBTVEsb0JBQW9CbkUsa0RBQVdBO29EQUFDLENBQUMrRDtZQUNyQyxNQUFNSyxRQUFRTCxFQUFFTSxNQUFNLENBQUNELEtBQUs7WUFDNUJiLGNBQWNhO1lBQ2RYLFdBQVdDLE9BQU8sR0FBR1U7UUFDdkI7bURBQUcsRUFBRTtJQUVMLHFCQUNFLDhEQUFDaEQ7UUFBSUQsV0FBVTs7MEJBQ2IsOERBQUNDO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDZix1REFBS0E7Z0NBQ0prRSxLQUFLZDtnQ0FDTFksT0FBT2Q7Z0NBQ1BpQixVQUFVSjtnQ0FDVkssWUFBWVY7Z0NBQ1pXLGFBQWEsV0FBd0MsT0FBN0J0QyxpQkFBaUIsYUFBWTtnQ0FDckRpQixVQUFVQTtnQ0FDVmpDLFdBQVU7Ozs7OzswQ0FHWiw4REFBQ0M7Z0NBQUlELFdBQVU7MENBQ1ppQywwQkFDQyw4REFBQ2hDO29DQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OztrQ0FLckIsOERBQUNoQix5REFBTUE7d0JBQ0x1RSxTQUFTZjt3QkFDVFAsVUFBVSxDQUFDRSxXQUFXTyxJQUFJLE1BQU1UO3dCQUNoQ2pDLFdBQVU7a0NBRVYsNEVBQUNYLDRHQUFJQTs0QkFBQ1csV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBS3BCLDhEQUFDQztnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUM0QjtrQ0FBSzs7Ozs7O29CQUNMTSw2QkFDQyw4REFBQ047d0JBQUs1QixXQUFVO2tDQUErQjs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBS3pEO01BcEVNK0I7QUFzRU4sTUFBTXlCLDhCQUFnQjFFLElBQUFBLDJDQUFJQSxXQUFDO1FBQUMsRUFBRTJFLElBQUksRUFBRUMsTUFBTSxFQUFFQyxlQUFlLEVBQXNCO1FBYTlDRixpQkFDREEsa0JBaUI1QkEsa0JBQXVCQSxrQkFpVE9BLGtCQUE0QkEsa0JBRTNDQSw2QkFBQUEsc0JBQUFBLGtCQU9rQ0E7O0lBeFZyRCxNQUFNLENBQUNHLFVBQVVDLFlBQVksR0FBR25GLCtDQUFRQSxDQUFZLEVBQUU7SUFDdEQsTUFBTSxDQUFDb0YsU0FBU0MsV0FBVyxHQUFHckYsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDc0YsU0FBU0MsV0FBVyxHQUFHdkYsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDaUQsa0JBQWtCdUMsb0JBQW9CLEdBQUd4RiwrQ0FBUUEsQ0FBQztJQUN6RCxNQUFNLENBQUN3RCxhQUFhaUMsZUFBZSxHQUFHekYsK0NBQVFBLENBQUM7SUFDL0MsTUFBTSxDQUFDMEYsVUFBVUMsWUFBWSxHQUFHM0YsK0NBQVFBLENBQUM7SUFDekMsTUFBTTRGLGlCQUFpQjFGLDZDQUFNQSxDQUFpQjtJQUM5QyxNQUFNMkYsc0JBQXNCM0YsNkNBQU1BLENBQStCO0lBQ2pFLE1BQU00RixzQkFBc0I1Riw2Q0FBTUEsQ0FBd0I7SUFDMUQsTUFBTTZGLG1CQUFtQjdGLDZDQUFNQSxDQUF3QjtJQUV2RCx5RUFBeUU7SUFDekUsTUFBTThGLG9CQUFvQjlGLDZDQUFNQSxFQUFDNkUsa0JBQUFBLEtBQUtrQixTQUFTLGNBQWRsQixzQ0FBQUEsZ0JBQWdCbUIsS0FBSztJQUN0RCxNQUFNQyxtQkFBbUJqRyw2Q0FBTUEsRUFBQzZFLG1CQUFBQSxLQUFLa0IsU0FBUyxjQUFkbEIsdUNBQUFBLGlCQUFnQnFCLElBQUk7SUFFcEQsNEJBQTRCO0lBQzVCbkcsZ0RBQVNBO21DQUFDO1lBQ1IsTUFBTW9HO3VEQUFjO29CQUNsQlYsWUFBWVcsT0FBT0MsVUFBVSxHQUFHO2dCQUNsQzs7WUFFQUY7WUFDQUMsT0FBT0UsZ0JBQWdCLENBQUMsVUFBVUg7WUFDbEM7MkNBQU8sSUFBTUMsT0FBT0csbUJBQW1CLENBQUMsVUFBVUo7O1FBQ3BEO2tDQUFHLEVBQUU7SUFFTCxnQ0FBZ0M7SUFDaENwRyxnREFBU0E7bUNBQUM7Z0JBQ29COEUsaUJBQ0RBO1lBRDNCaUIsa0JBQWtCbkMsT0FBTyxJQUFHa0Isa0JBQUFBLEtBQUtrQixTQUFTLGNBQWRsQixzQ0FBQUEsZ0JBQWdCbUIsS0FBSztZQUNqREMsaUJBQWlCdEMsT0FBTyxJQUFHa0IsbUJBQUFBLEtBQUtrQixTQUFTLGNBQWRsQix1Q0FBQUEsaUJBQWdCcUIsSUFBSTtRQUNqRDtrQ0FBRztTQUFDckIsbUJBQUFBLEtBQUtrQixTQUFTLGNBQWRsQix1Q0FBQUEsaUJBQWdCbUIsS0FBSztTQUFFbkIsbUJBQUFBLEtBQUtrQixTQUFTLGNBQWRsQix1Q0FBQUEsaUJBQWdCcUIsSUFBSTtLQUFDO0lBRWhEbkcsZ0RBQVNBO21DQUFDO1lBQ1J5RztZQUNBOzJDQUFPO29CQUNMLHVDQUF1QztvQkFDdkMsSUFBSWIsb0JBQW9CaEMsT0FBTyxFQUFFO3dCQUMvQmdDLG9CQUFvQmhDLE9BQU8sQ0FBQzhDLEtBQUs7b0JBQ25DO29CQUNBLDRCQUE0QjtvQkFDNUIsSUFBSWIsb0JBQW9CakMsT0FBTyxFQUFFO3dCQUMvQitDLGFBQWFkLG9CQUFvQmpDLE9BQU87b0JBQzFDO29CQUNBLHlCQUF5QjtvQkFDekIsSUFBSWtDLGlCQUFpQmxDLE9BQU8sRUFBRTt3QkFDNUIrQyxhQUFhYixpQkFBaUJsQyxPQUFPO29CQUN2QztnQkFDRjs7UUFDRjtrQ0FBRztRQUFDa0IsS0FBSzhCLEVBQUU7S0FBQztJQUVaLHVEQUF1RDtJQUN2RCxNQUFNQyxvQkFBb0I1Ryw2Q0FBTUE7bURBQWEsS0FBTzs7SUFDcEQ0RyxrQkFBa0JqRCxPQUFPLEdBQUc7WUFDMUIrQjtTQUFBQSwwQkFBQUEsZUFBZS9CLE9BQU8sY0FBdEIrQiw4Q0FBQUEsd0JBQXdCbUIsY0FBYyxDQUFDO1lBQUVDLFVBQVU7UUFBUztJQUM5RDtJQUVBLCtEQUErRDtJQUMvRCxNQUFNQyxtQkFBbUIvRyw2Q0FBTUEsQ0FBQ2dGLFNBQVNnQyxNQUFNO0lBQy9DakgsZ0RBQVNBO21DQUFDO1lBQ1IsSUFBSWlGLFNBQVNnQyxNQUFNLEdBQUdELGlCQUFpQnBELE9BQU8sRUFBRTtnQkFDOUNvRCxpQkFBaUJwRCxPQUFPLEdBQUdxQixTQUFTZ0MsTUFBTTtnQkFDMUMsTUFBTUMsWUFBWUM7eURBQVc7NEJBQzNCTjt5QkFBQUEsNkJBQUFBLGtCQUFrQmpELE9BQU8sY0FBekJpRCxpREFBQUEsZ0NBQUFBO29CQUNGO3dEQUFHO2dCQUNIOytDQUFPLElBQU1GLGFBQWFPOztZQUM1QjtZQUNBRixpQkFBaUJwRCxPQUFPLEdBQUdxQixTQUFTZ0MsTUFBTTtRQUM1QztrQ0FBRztRQUFDaEMsU0FBU2dDLE1BQU07S0FBQztJQUVwQiw0RUFBNEU7SUFDNUUsTUFBTUcsaUJBQWlCbkgsNkNBQU1BLENBQUNzRDtJQUM5QnZELGdEQUFTQTttQ0FBQztZQUNSLElBQUl1RCxlQUFlLENBQUM2RCxlQUFleEQsT0FBTyxFQUFFO2dCQUMxQyx5QkFBeUI7Z0JBQ3pCLE1BQU1zRCxZQUFZQzt5REFBVzs0QkFDM0JOO3lCQUFBQSw2QkFBQUEsa0JBQWtCakQsT0FBTyxjQUF6QmlELGlEQUFBQSxnQ0FBQUE7b0JBQ0Y7d0RBQUc7Z0JBQ0hPLGVBQWV4RCxPQUFPLEdBQUdMO2dCQUN6QjsrQ0FBTyxJQUFNb0QsYUFBYU87O1lBQzVCO1lBQ0FFLGVBQWV4RCxPQUFPLEdBQUdMO1FBQzNCO2tDQUFHO1FBQUNBO0tBQVk7SUFFaEIsTUFBTWtELGVBQWU7UUFDbkIsSUFBSTtZQUNGckIsV0FBVztZQUNYLE1BQU1pQyxXQUFXLE1BQU1qSCx1REFBV0EsQ0FBQ2tILGVBQWUsQ0FBQ3hDLEtBQUs4QixFQUFFLEVBQUU7Z0JBQUVXLE9BQU87WUFBRztZQUN4RXJDLFlBQVltQyxTQUFTRyxJQUFJLENBQUNDLE9BQU8sS0FBSywrQkFBK0I7UUFDdkUsRUFBRSxPQUFPQyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1FBQzVDLFNBQVU7WUFDUnRDLFdBQVc7UUFDYjtJQUNGO0lBRUEsTUFBTXZCLG9CQUFvQjNELGtEQUFXQTt3REFBQyxPQUFPMEg7WUFDM0MsSUFBSSxDQUFDQSxZQUFZN0QsSUFBSSxNQUFNc0IsU0FBUztZQUVwQyxNQUFNd0MsU0FBUyxRQUFtQixPQUFYckYsS0FBS3NGLEdBQUc7WUFDL0J4QyxXQUFXO1lBRVgsSUFBSTtnQkFDRixxQ0FBcUM7Z0JBQ3JDLE1BQU15QyxjQUF1QjtvQkFDM0JuQixJQUFJaUI7b0JBQ0oxRyxNQUFNO29CQUNORCxTQUFTMEc7b0JBQ1RJLGFBQWE7b0JBQ2JsRixXQUFXLElBQUlOLE9BQU95RixXQUFXO29CQUNqQ0MsV0FBVyxJQUFJMUYsT0FBT3lGLFdBQVc7Z0JBQ25DO2dCQUVBL0M7b0VBQVlpRCxDQUFBQSxPQUFROytCQUFJQTs0QkFBTUo7eUJBQVk7O2dCQUUxQ0osUUFBUVMsR0FBRyxDQUFDLDRCQUE0QnRELEtBQUs4QixFQUFFLEVBQUU7b0JBQUV6RSxTQUFTeUY7b0JBQWFTLFdBQVc7Z0JBQUs7Z0JBRXpGLHNDQUFzQztnQkFDdEMsTUFBTWhCLFdBQVcsTUFBTWpILHVEQUFXQSxDQUFDa0ksV0FBVyxDQUFDeEQsS0FBSzhCLEVBQUUsRUFBRTtvQkFDdER6RSxTQUFTeUY7b0JBQ1RTLFdBQVc7Z0JBQ2I7Z0JBRUFWLFFBQVFTLEdBQUcsQ0FBQyw4QkFBOEJmO2dCQUUxQyx5REFBeUQ7Z0JBQ3pELElBQUlBLFNBQVNULEVBQUUsRUFBRTtvQkFDZjFCO3dFQUFZaUQsQ0FBQUEsT0FBUUEsS0FBS0ksR0FBRztnRkFBQ0MsQ0FBQUEsTUFDM0JBLElBQUk1QixFQUFFLEtBQUtpQixTQUFTO3dDQUFFLEdBQUdXLEdBQUc7d0NBQUU1QixJQUFJUyxTQUFTVCxFQUFFO29DQUFDLElBQUk0Qjs7O2dCQUV0RDtnQkFFQSwyQkFBMkI7Z0JBQzNCLE1BQU1DO1lBRVIsRUFBRSxPQUFPZixPQUFPO2dCQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtnQkFDekMsNkNBQTZDO2dCQUM3Q3hDO29FQUFZaUQsQ0FBQUEsT0FBUUEsS0FBS08sTUFBTTs0RUFBQ0YsQ0FBQUEsTUFBT0EsSUFBSTVCLEVBQUUsS0FBS2lCOzs7Z0JBQ2xEYyxNQUFNO1lBQ1IsU0FBVTtnQkFDUnJELFdBQVc7WUFDYjtRQUNGO3VEQUFHO1FBQUNEO1FBQVNQLEtBQUs4QixFQUFFO0tBQUM7SUFFckIsTUFBTTZCLGlCQUFpQjtRQUNyQmQsUUFBUVMsR0FBRyxDQUFDLDZCQUE2QnRELEtBQUs4QixFQUFFO1FBQ2hEcEIsZUFBZTtRQUNmRCxvQkFBb0I7UUFFcEIsK0NBQStDO1FBQy9DLElBQUlLLG9CQUFvQmhDLE9BQU8sRUFBRTtZQUMvQmdDLG9CQUFvQmhDLE9BQU8sQ0FBQzhDLEtBQUs7UUFDbkM7UUFDQSxJQUFJYixvQkFBb0JqQyxPQUFPLEVBQUU7WUFDL0IrQyxhQUFhZCxvQkFBb0JqQyxPQUFPO1FBQzFDO1FBQ0EsSUFBSWtDLGlCQUFpQmxDLE9BQU8sRUFBRTtZQUM1QitDLGFBQWFiLGlCQUFpQmxDLE9BQU87UUFDdkM7UUFFQSxJQUFJZ0YsMEJBQTBCO1FBRTlCLDJEQUEyRDtRQUMzRCxJQUFJQyxpQkFBaUI7UUFDckIsTUFBTUMseUJBQXlCLENBQUMzRztZQUM5QixNQUFNMkYsTUFBTXRGLEtBQUtzRixHQUFHO1lBQ3BCLElBQUlqQyxvQkFBb0JqQyxPQUFPLEVBQUU7Z0JBQy9CK0MsYUFBYWQsb0JBQW9CakMsT0FBTztZQUMxQztZQUVBLGtFQUFrRTtZQUNsRSxJQUFJa0UsTUFBTWUsaUJBQWlCLEtBQUs7Z0JBQzlCdEQsb0JBQW9CcEQ7Z0JBQ3BCMEcsaUJBQWlCZjtZQUNuQixPQUFPO2dCQUNMLGlDQUFpQztnQkFDakNqQyxvQkFBb0JqQyxPQUFPLEdBQUd1RCxXQUFXO29CQUN2QzVCLG9CQUFvQnBEO29CQUNwQjBHLGlCQUFpQnJHLEtBQUtzRixHQUFHO2dCQUMzQixHQUFHO1lBQ0w7UUFDRjtRQUVBLE1BQU1pQixZQUFZLENBQUN2QjtZQUNqQkcsUUFBUVMsR0FBRyxDQUFDLHVCQUF1Qlo7WUFFbkMsT0FBUUEsS0FBS3dCLEtBQUs7Z0JBQ2hCLEtBQUs7b0JBQ0hyQixRQUFRUyxHQUFHLENBQUM7b0JBQ1pRLDBCQUEwQjtvQkFDMUJyRCxvQkFBb0I7b0JBQ3BCO2dCQUNGLEtBQUs7b0JBQ0hxRCwyQkFBMkJwQixLQUFLQSxJQUFJO29CQUNwQ3NCLHVCQUF1QkY7b0JBQ3ZCO2dCQUNGLEtBQUs7b0JBQ0hqQixRQUFRUyxHQUFHLENBQUMsb0JBQW9CWixLQUFLQSxJQUFJO29CQUN6QztnQkFDRixLQUFLO3dCQVlHQTtvQkFYTkcsUUFBUVMsR0FBRyxDQUFDLGdDQUFnQ1E7b0JBQzVDLHNDQUFzQztvQkFDdEMsSUFBSS9DLG9CQUFvQmpDLE9BQU8sRUFBRTt3QkFDL0IrQyxhQUFhZCxvQkFBb0JqQyxPQUFPO29CQUMxQztvQkFDQSx1REFBdUQ7b0JBQ3ZELElBQUlrQyxpQkFBaUJsQyxPQUFPLEVBQUU7d0JBQzVCK0MsYUFBYWIsaUJBQWlCbEMsT0FBTztvQkFDdkM7b0JBQ0EsaUNBQWlDO29CQUNqQyxNQUFNcUYsZUFBd0I7d0JBQzVCckMsSUFBSVksRUFBQUEsYUFBQUEsS0FBS0EsSUFBSSxjQUFUQSxpQ0FBQUEsV0FBVzBCLGFBQWEsS0FBSSxPQUFrQixPQUFYMUcsS0FBS3NGLEdBQUc7d0JBQy9DM0csTUFBTTt3QkFDTkQsU0FBUzBIO3dCQUNUWixhQUFhO3dCQUNibEYsV0FBVyxJQUFJTixPQUFPeUYsV0FBVzt3QkFDakNDLFdBQVcsSUFBSTFGLE9BQU95RixXQUFXO29CQUNuQztvQkFFQS9DLFlBQVlpRCxDQUFBQSxPQUFROytCQUFJQTs0QkFBTWM7eUJBQWE7b0JBQzNDMUQsb0JBQW9CO29CQUNwQkMsZUFBZTtvQkFDZixJQUFJSSxvQkFBb0JoQyxPQUFPLEVBQUU7d0JBQy9CZ0Msb0JBQW9CaEMsT0FBTyxDQUFDOEMsS0FBSztvQkFDbkM7b0JBQ0E7Z0JBQ0Y7b0JBQ0VpQixRQUFRUyxHQUFHLENBQUMsc0JBQXNCWixLQUFLd0IsS0FBSztZQUNoRDtRQUNGO1FBRUEsTUFBTUcsVUFBVSxPQUFPekI7Z0JBa0JoQkE7WUFqQkxDLFFBQVFELEtBQUssQ0FBQyxpQkFBaUJBO1lBRS9CLGlEQUFpRDtZQUNqRCxJQUFJN0Isb0JBQW9CakMsT0FBTyxFQUFFO2dCQUMvQitDLGFBQWFkLG9CQUFvQmpDLE9BQU87WUFDMUM7WUFDQSxJQUFJa0MsaUJBQWlCbEMsT0FBTyxFQUFFO2dCQUM1QitDLGFBQWFiLGlCQUFpQmxDLE9BQU87WUFDdkM7WUFFQTRCLGVBQWU7WUFDZkQsb0JBQW9CO1lBQ3BCLElBQUlLLG9CQUFvQmhDLE9BQU8sRUFBRTtnQkFDL0JnQyxvQkFBb0JoQyxPQUFPLENBQUM4QyxLQUFLO1lBQ25DO1lBRUEscUZBQXFGO1lBQ3JGLElBQUksR0FBQ2dCLGlCQUFBQSxNQUFNdkYsT0FBTyxjQUFidUYscUNBQUFBLGVBQWUwQixRQUFRLENBQUMsZUFBYzdGLGFBQWE7Z0JBQ3REb0UsUUFBUVMsR0FBRyxDQUFDO2dCQUNaLElBQUk7b0JBQ0YsTUFBTTNCO2dCQUNSLEVBQUUsT0FBTzRDLGFBQWE7b0JBQ3BCMUIsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QjJCO2dCQUM1QywrREFBK0Q7Z0JBQ2pFO1lBQ0Y7UUFDRjtRQUVBLElBQUk7WUFDRiwrQkFBK0I7WUFDL0IsTUFBTUMsYUFBYSxNQUFNbEosdURBQVdBLENBQUNtSixzQkFBc0IsQ0FBQ3pFLEtBQUs4QixFQUFFLEVBQUVtQyxXQUFXSTtZQUNoRnZELG9CQUFvQmhDLE9BQU8sR0FBRzBGO1lBRTlCLDJFQUEyRTtZQUMzRXhELGlCQUFpQmxDLE9BQU8sR0FBR3VELFdBQVc7Z0JBQ3BDLDBFQUEwRTtnQkFDMUUsSUFBSTVELGVBQWVxQyxvQkFBb0JoQyxPQUFPLEVBQUU7b0JBQzlDK0QsUUFBUVMsR0FBRyxDQUFDO29CQUNaZSxRQUFRLElBQUlLLE1BQU07Z0JBQ3BCO1lBQ0YsR0FBRztRQUVMLEVBQUUsT0FBTzlCLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVDQUF1Q0E7WUFDckRsQyxlQUFlO1lBQ2ZtRCxNQUFNO1FBQ1I7SUFDRjtJQUVBLElBQUl4RCxTQUFTO1FBQ1gscUJBQ0UsOERBQUM3RDtZQUFJRCxXQUFVOzs4QkFDYiw4REFBQ0M7b0JBQUlELFdBQVU7OEJBQ2IsNEVBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7Z0NBQUlELFdBQVU7Ozs7OzswQ0FDZiw4REFBQ0M7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDQzt3Q0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBSXJCLDhEQUFDQztvQkFBSUQsV0FBVTs4QkFDWm9JLE1BQU1DLElBQUksQ0FBQzt3QkFBRXpDLFFBQVE7b0JBQUUsR0FBR3NCLEdBQUcsQ0FBQyxDQUFDb0IsR0FBR0Msa0JBQ2pDLDhEQUFDdEk7NEJBQVlELFdBQVU7OzhDQUNyQiw4REFBQ0M7b0NBQUlELFdBQVU7Ozs7Ozs4Q0FDZiw4REFBQ0M7b0NBQUlELFdBQVU7O3NEQUNiLDhEQUFDQzs0Q0FBSUQsV0FBVTs7Ozs7O3NEQUNmLDhEQUFDQzs0Q0FBSUQsV0FBVTs7Ozs7Ozs7Ozs7OzsyQkFKVHVJOzs7Ozs7Ozs7Ozs7Ozs7O0lBV3BCO0lBRUEscUJBQ0UsOERBQUN0STtRQUFJRCxXQUFVOzswQkFFYiw4REFBQ0M7Z0JBQUlELFdBQVU7MEJBQ2IsNEVBQUNDO29CQUFJRCxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUlELFdBQVU7O2dDQUVab0UsWUFBWVYsd0JBQ1gsOERBQUMxRSx5REFBTUE7b0NBQ0x3SixTQUFRO29DQUNSQyxNQUFLO29DQUNMbEYsU0FBU0c7b0NBQ1QxRCxXQUFVO29DQUNWMEksY0FBVzs4Q0FFWCw0RUFBQ25KLDZHQUFTQTt3Q0FBQ1MsV0FBVTs7Ozs7Ozs7Ozs7OENBSXpCLDhEQUFDQztvQ0FDQ0QsV0FBVywrQkFFVixPQURDb0UsWUFBWVQsa0JBQWtCLDJFQUEyRTtvQ0FFM0dKLFNBQVNhLFlBQVlULGtCQUFrQkEsa0JBQWtCZ0Y7O3NEQUV6RCw4REFBQzFJOzRDQUFJRCxXQUFVOzs4REFDYiw4REFBQ2QseURBQU1BO29EQUFDYyxXQUFVOztzRUFDaEIsOERBQUNaLDhEQUFXQTs0REFBQ2lDLEdBQUcsR0FBRW9DLG1CQUFBQSxLQUFLa0IsU0FBUyxjQUFkbEIsdUNBQUFBLGlCQUFnQm1CLEtBQUs7NERBQUV0RCxHQUFHLEdBQUVtQyxtQkFBQUEsS0FBS2tCLFNBQVMsY0FBZGxCLHVDQUFBQSxpQkFBZ0JxQixJQUFJOzs7Ozs7c0VBQ2xFLDhEQUFDM0YsaUVBQWNBOzREQUFDYSxXQUFVO3NFQUN2QnlELEVBQUFBLG1CQUFBQSxLQUFLa0IsU0FBUyxjQUFkbEIsd0NBQUFBLHVCQUFBQSxpQkFBZ0JxQixJQUFJLGNBQXBCckIsNENBQUFBLDhCQUFBQSxxQkFBc0JsQyxNQUFNLENBQUMsZ0JBQTdCa0Msa0RBQUFBLDRCQUFpQ2pDLFdBQVcsT0FBTTs7Ozs7Ozs7Ozs7OzhEQUl2RCw4REFBQ3ZCO29EQUFJRCxXQUFVOzs7Ozs7Ozs7Ozs7c0RBRWpCLDhEQUFDQzs7OERBQ0MsOERBQUMySTtvREFBRzVJLFdBQVU7OERBQXlCeUQsRUFBQUEsbUJBQUFBLEtBQUtrQixTQUFTLGNBQWRsQix1Q0FBQUEsaUJBQWdCcUIsSUFBSSxLQUFJOzs7Ozs7OERBQy9ELDhEQUFDN0U7b0RBQUlELFdBQVU7O3NFQUNiLDhEQUFDNEI7O2dFQUFNNkIsS0FBS29GLFlBQVk7Z0VBQUM7Ozs7Ozs7c0VBQ3pCLDhEQUFDakg7c0VBQUs7Ozs7OztzRUFDTiw4REFBQ0E7NERBQUs1QixXQUFVO3NFQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQUt6Qyw4REFBQ0M7NEJBQUlELFdBQVU7O2dDQUVab0UsWUFBWVQsaUNBQ1gsOERBQUMzRSx5REFBTUE7b0NBQ0x3SixTQUFRO29DQUNSQyxNQUFLO29DQUNMbEYsU0FBU0k7b0NBQ1QzRCxXQUFVO29DQUNWMEksY0FBVzs4Q0FFWCw0RUFBQ2xKLDZHQUFJQTt3Q0FBQ1EsV0FBVTs7Ozs7Ozs7Ozs7OENBSXBCLDhEQUFDaEIseURBQU1BO29DQUFDd0osU0FBUTtvQ0FBUUMsTUFBSztvQ0FBT3pJLFdBQVU7OENBQzVDLDRFQUFDViw2R0FBWUE7d0NBQUNVLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT2hDLDhEQUFDQztnQkFBSUQsV0FBVTs7b0JBQ1o0RCxTQUFTc0QsR0FBRyxDQUFDLENBQUNwRyx3QkFDYiw4REFBQ0Q7NEJBRUNDLFNBQVNBOzRCQUNUQyxnQkFBZ0IyRCxrQkFBa0JuQyxPQUFPOzRCQUN6Q3ZCLGVBQWU2RCxpQkFBaUJ0QyxPQUFPOzJCQUhsQ3pCLFFBQVF5RSxFQUFFOzs7OztvQkFRbEJyRCw2QkFDQyw4REFBQ1I7d0JBQ0NDLGtCQUFrQkE7d0JBQ2xCWixnQkFBZ0IyRCxrQkFBa0JuQyxPQUFPO3dCQUN6Q3ZCLGVBQWU2RCxpQkFBaUJ0QyxPQUFPOzs7Ozs7a0NBSTNDLDhEQUFDdEM7d0JBQUlrRCxLQUFLbUI7Ozs7Ozs7Ozs7OzswQkFJWiw4REFBQ3ZDO2dCQUNDQyxlQUFlUTtnQkFDZlAsVUFBVStCLFdBQVc5QjtnQkFDckJsQixlQUFlNkQsaUJBQWlCdEMsT0FBTztnQkFDdkNMLGFBQWFBOzs7Ozs7Ozs7Ozs7QUFJckI7O0FBRUFzQixjQUFjc0YsV0FBVyxHQUFHO0FBRUgiLCJzb3VyY2VzIjpbIkU6XFxiZXNzdGlla3VcXGZyb250ZW5kXFxzcmNcXGNvbXBvbmVudHNcXGNoYXRcXGNoYXQtaW50ZXJmYWNlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjaywgbWVtbyB9IGZyb20gJ3JlYWN0JztcclxuaW1wb3J0IHsgQ2hhdCwgTWVzc2FnZSwgU1NFRXZlbnQgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xyXG5pbXBvcnQgeyBjaGF0U2VydmljZSB9IGZyb20gJ0Avc2VydmljZXMvY2hhdCc7XHJcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nO1xyXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9pbnB1dCc7XHJcbmltcG9ydCB7IEF2YXRhciwgQXZhdGFyRmFsbGJhY2ssIEF2YXRhckltYWdlIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2F2YXRhcic7XHJcbmltcG9ydCB7IFNlbmQsIE1vcmVWZXJ0aWNhbCwgQXJyb3dMZWZ0LCBJbmZvIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuaW1wb3J0IHsgZm9ybWF0RGlzdGFuY2VUb05vdyB9IGZyb20gJ2RhdGUtZm5zJztcclxuaW1wb3J0IFJlYWN0TWFya2Rvd24gZnJvbSAncmVhY3QtbWFya2Rvd24nO1xyXG5pbXBvcnQgcmVtYXJrR2ZtIGZyb20gJ3JlbWFyay1nZm0nO1xyXG5cclxuaW50ZXJmYWNlIENoYXRJbnRlcmZhY2VQcm9wcyB7XHJcbiAgY2hhdDogQ2hhdDtcclxuICBvbkJhY2s/OiAoKSA9PiB2b2lkO1xyXG4gIG9uVG9nZ2xlUHJvZmlsZT86ICgpID0+IHZvaWQ7XHJcbn1cclxuXHJcbmludGVyZmFjZSBNZXNzYWdlQ29udGVudFByb3BzIHtcclxuICBjb250ZW50OiBzdHJpbmc7XHJcbiAgcm9sZTogJ3VzZXInIHwgJ2Fzc2lzdGFudCc7XHJcbn1cclxuXHJcbmNvbnN0IE1lc3NhZ2VDb250ZW50ID0gbWVtbygoeyBjb250ZW50LCByb2xlIH06IE1lc3NhZ2VDb250ZW50UHJvcHMpID0+IHtcclxuICBpZiAocm9sZSA9PT0gJ3VzZXInKSB7XHJcbiAgICAvLyBGb3IgdXNlciBtZXNzYWdlcywganVzdCBkaXNwbGF5IGFzIHBsYWluIHRleHQgd2l0aCBsaW5lIGJyZWFrc1xyXG4gICAgcmV0dXJuIDxwIGNsYXNzTmFtZT1cInRleHQtc20gd2hpdGVzcGFjZS1wcmUtd3JhcCBsZWFkaW5nLXJlbGF4ZWRcIj57Y29udGVudH08L3A+O1xyXG4gIH1cclxuXHJcbiAgLy8gRm9yIEFJIG1lc3NhZ2VzLCByZW5kZXIgYXMgTWFya2Rvd25cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIGxlYWRpbmctcmVsYXhlZCBwcm9zZSBwcm9zZS1zbSBtYXgtdy1ub25lIGRhcms6cHJvc2UtaW52ZXJ0XCI+XHJcbiAgICAgIDxSZWFjdE1hcmtkb3duXHJcbiAgICAgICAgcmVtYXJrUGx1Z2lucz17W3JlbWFya0dmbV19XHJcbiAgICAgICAgY29tcG9uZW50cz17e1xyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIHBhcmFncmFwaCBzdHlsaW5nXHJcbiAgICAgICAgICBwOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cIm1iLTIgbGFzdDptYi0wIGxlYWRpbmctcmVsYXhlZFwiPntjaGlsZHJlbn08L3A+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIGVtcGhhc2lzIChpdGFsaWMpXHJcbiAgICAgICAgICBlbTogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8ZW0gY2xhc3NOYW1lPVwiaXRhbGljIHRleHQtaW5oZXJpdFwiPntjaGlsZHJlbn08L2VtPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIC8vIEN1c3RvbWl6ZSBzdHJvbmcgKGJvbGQpXHJcbiAgICAgICAgICBzdHJvbmc6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPHN0cm9uZyBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkIHRleHQtaW5oZXJpdFwiPntjaGlsZHJlbn08L3N0cm9uZz5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgICAvLyBDdXN0b21pemUgY29kZSBibG9ja3NcclxuICAgICAgICAgIGNvZGU6ICh7IGNoaWxkcmVuLCBjbGFzc05hbWUgfSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBpc0lubGluZSA9ICFjbGFzc05hbWU7XHJcbiAgICAgICAgICAgIGlmIChpc0lubGluZSkge1xyXG4gICAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8Y29kZSBjbGFzc05hbWU9XCJiZy1tdXRlZCBweC0xLjUgcHktMC41IHJvdW5kZWQgdGV4dC14cyBmb250LW1vbm9cIj5cclxuICAgICAgICAgICAgICAgICAge2NoaWxkcmVufVxyXG4gICAgICAgICAgICAgICAgPC9jb2RlPlxyXG4gICAgICAgICAgICAgICk7XHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICA8Y29kZSBjbGFzc05hbWU9XCJibG9jayBiZy1tdXRlZCBwLTMgcm91bmRlZC1sZyB0ZXh0LXhzIGZvbnQtbW9ubyBvdmVyZmxvdy14LWF1dG9cIj5cclxuICAgICAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgICA8L2NvZGU+XHJcbiAgICAgICAgICAgICk7XHJcbiAgICAgICAgICB9LFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIGxpc3RzXHJcbiAgICAgICAgICB1bDogKHsgY2hpbGRyZW4gfSkgPT4gKFxyXG4gICAgICAgICAgICA8dWwgY2xhc3NOYW1lPVwibGlzdC1kaXNjIGxpc3QtaW5zaWRlIG1iLTIgc3BhY2UteS0xXCI+e2NoaWxkcmVufTwvdWw+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgb2w6ICh7IGNoaWxkcmVuIH0pID0+IChcclxuICAgICAgICAgICAgPG9sIGNsYXNzTmFtZT1cImxpc3QtZGVjaW1hbCBsaXN0LWluc2lkZSBtYi0yIHNwYWNlLXktMVwiPntjaGlsZHJlbn08L29sPlxyXG4gICAgICAgICAgKSxcclxuICAgICAgICAgIGxpOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxsaSBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e2NoaWxkcmVufTwvbGk+XHJcbiAgICAgICAgICApLFxyXG4gICAgICAgICAgLy8gQ3VzdG9taXplIGJsb2NrcXVvdGVzXHJcbiAgICAgICAgICBibG9ja3F1b3RlOiAoeyBjaGlsZHJlbiB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxibG9ja3F1b3RlIGNsYXNzTmFtZT1cImJvcmRlci1sLTQgYm9yZGVyLW11dGVkIHBsLTQgaXRhbGljIG15LTJcIj5cclxuICAgICAgICAgICAgICB7Y2hpbGRyZW59XHJcbiAgICAgICAgICAgIDwvYmxvY2txdW90ZT5cclxuICAgICAgICAgICksXHJcbiAgICAgICAgfX1cclxuICAgICAgPlxyXG4gICAgICAgIHtjb250ZW50fVxyXG4gICAgICA8L1JlYWN0TWFya2Rvd24+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmludGVyZmFjZSBNZXNzYWdlSXRlbVByb3BzIHtcclxuICBtZXNzYWdlOiBNZXNzYWdlO1xyXG4gIGNoYXJhY3RlckltYWdlPzogc3RyaW5nO1xyXG4gIGNoYXJhY3Rlck5hbWU/OiBzdHJpbmc7XHJcbn1cclxuXHJcbmNvbnN0IE1lc3NhZ2VJdGVtID0gbWVtbygoeyBtZXNzYWdlLCBjaGFyYWN0ZXJJbWFnZSwgY2hhcmFjdGVyTmFtZSB9OiBNZXNzYWdlSXRlbVByb3BzKSA9PiB7XHJcbiAgY29uc3QgZm9ybWF0VGltZSA9IHVzZUNhbGxiYWNrKChkYXRlU3RyaW5nOiBzdHJpbmcpID0+IHtcclxuICAgIHRyeSB7XHJcbiAgICAgIHJldHVybiBmb3JtYXREaXN0YW5jZVRvTm93KG5ldyBEYXRlKGRhdGVTdHJpbmcpLCB7IGFkZFN1ZmZpeDogdHJ1ZSB9KTtcclxuICAgIH0gY2F0Y2gge1xyXG4gICAgICByZXR1cm4gJyc7XHJcbiAgICB9XHJcbiAgfSwgW10pO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdlxyXG4gICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWVuZCBnYXAtMiAke21lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInID8gJ2p1c3RpZnktZW5kJyA6ICdqdXN0aWZ5LXN0YXJ0J31gfVxyXG4gICAgPlxyXG4gICAgICB7LyogQ2hhcmFjdGVyIEF2YXRhciBmb3IgYXNzaXN0YW50IG1lc3NhZ2VzICovfVxyXG4gICAgICB7bWVzc2FnZS5yb2xlID09PSAnYXNzaXN0YW50JyAmJiAoXHJcbiAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTggaC04IG1iLTFcIj5cclxuICAgICAgICAgIDxBdmF0YXJJbWFnZSBzcmM9e2NoYXJhY3RlckltYWdlfSBhbHQ9e2NoYXJhY3Rlck5hbWV9IC8+XHJcbiAgICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1bIzJERDRCRl0vMTAgdGV4dC1bIzJERDRCRl1cIj5cclxuICAgICAgICAgICAge2NoYXJhY3Rlck5hbWU/LmNoYXJBdCgwKT8udG9VcHBlckNhc2UoKSB8fCAnQyd9XHJcbiAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgIDwvQXZhdGFyPlxyXG4gICAgICApfVxyXG5cclxuICAgICAgPGRpdlxyXG4gICAgICAgIGNsYXNzTmFtZT17YG1heC13LVs3MCVdIHJvdW5kZWQtMnhsIHAtNCBzaGFkb3ctc20gJHtcclxuICAgICAgICAgIG1lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInXHJcbiAgICAgICAgICAgID8gJ2JnLVsjMkRENEJGXSB0ZXh0LXdoaXRlIHJvdW5kZWQtYnItbWQnXHJcbiAgICAgICAgICAgIDogJ2JnLXdoaXRlIGRhcms6YmctbXV0ZWQgYm9yZGVyIHJvdW5kZWQtYmwtbWQnXHJcbiAgICAgICAgfWB9XHJcbiAgICAgID5cclxuICAgICAgICA8TWVzc2FnZUNvbnRlbnQgY29udGVudD17bWVzc2FnZS5jb250ZW50fSByb2xlPXttZXNzYWdlLnJvbGV9IC8+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gbXQtMiAke1xyXG4gICAgICAgICAgbWVzc2FnZS5yb2xlID09PSAndXNlcicgPyAndGV4dC13aGl0ZS83MCcgOiAndGV4dC1tdXRlZC1mb3JlZ3JvdW5kJ1xyXG4gICAgICAgIH1gfT5cclxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHNcIj5cclxuICAgICAgICAgICAge2Zvcm1hdFRpbWUobWVzc2FnZS5jcmVhdGVkQXQpfVxyXG4gICAgICAgICAgPC9wPlxyXG4gICAgICAgICAge21lc3NhZ2Uucm9sZSA9PT0gJ3VzZXInICYmIChcclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xIGgtMSBiZy13aGl0ZS83MCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTEgYmctd2hpdGUvNzAgcm91bmRlZC1mdWxsXCI+PC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKX1cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmludGVyZmFjZSBTdHJlYW1pbmdNZXNzYWdlUHJvcHMge1xyXG4gIHN0cmVhbWluZ01lc3NhZ2U6IHN0cmluZztcclxuICBjaGFyYWN0ZXJJbWFnZT86IHN0cmluZztcclxuICBjaGFyYWN0ZXJOYW1lPzogc3RyaW5nO1xyXG59XHJcblxyXG5jb25zdCBTdHJlYW1pbmdNZXNzYWdlID0gbWVtbygoeyBzdHJlYW1pbmdNZXNzYWdlLCBjaGFyYWN0ZXJJbWFnZSwgY2hhcmFjdGVyTmFtZSB9OiBTdHJlYW1pbmdNZXNzYWdlUHJvcHMpID0+IHtcclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBnYXAtMiBqdXN0aWZ5LXN0YXJ0XCI+XHJcbiAgICAgIDxBdmF0YXIgY2xhc3NOYW1lPVwidy04IGgtOCBtYi0xXCI+XHJcbiAgICAgICAgPEF2YXRhckltYWdlIHNyYz17Y2hhcmFjdGVySW1hZ2V9IGFsdD17Y2hhcmFjdGVyTmFtZX0gLz5cclxuICAgICAgICA8QXZhdGFyRmFsbGJhY2sgY2xhc3NOYW1lPVwidGV4dC14cyBiZy1bIzJERDRCRl0vMTAgdGV4dC1bIzJERDRCRl1cIj5cclxuICAgICAgICAgIHtjaGFyYWN0ZXJOYW1lPy5jaGFyQXQoMCk/LnRvVXBwZXJDYXNlKCkgfHwgJ0MnfVxyXG4gICAgICAgIDwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgIDwvQXZhdGFyPlxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtYXgtdy1bNzAlXSByb3VuZGVkLTJ4bCByb3VuZGVkLWJsLW1kIHAtNCBiZy13aGl0ZSBkYXJrOmJnLW11dGVkIGJvcmRlciBzaGFkb3ctc21cIj5cclxuICAgICAgICB7c3RyZWFtaW5nTWVzc2FnZSA/IChcclxuICAgICAgICAgIDxNZXNzYWdlQ29udGVudCBjb250ZW50PXtzdHJlYW1pbmdNZXNzYWdlfSByb2xlPVwiYXNzaXN0YW50XCIgLz5cclxuICAgICAgICApIDogKFxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlRoaW5raW5nPC9zcGFuPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0xIG1sLTJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctWyMyREQ0QkZdIHJvdW5kZWQtZnVsbCBhbmltYXRlLWJvdW5jZVwiIC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLVsjMkRENEJGXSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMXMnIH19IC8+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTIgaC0yIGJnLVsjMkRENEJGXSByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzAuMnMnIH19IC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBtdC0yIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMVwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMSBoLTEgYmctWyMyREQ0QkZdIHJvdW5kZWQtZnVsbCBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XHJcbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteHNcIj5UeXBpbmcuLi48L3NwYW4+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59KTtcclxuXHJcbmludGVyZmFjZSBDaGF0SW5wdXRQcm9wcyB7XHJcbiAgb25TZW5kTWVzc2FnZTogKG1lc3NhZ2U6IHN0cmluZykgPT4gdm9pZDtcclxuICBkaXNhYmxlZDogYm9vbGVhbjtcclxuICBjaGFyYWN0ZXJOYW1lPzogc3RyaW5nO1xyXG4gIGlzU3RyZWFtaW5nOiBib29sZWFuO1xyXG59XHJcblxyXG5jb25zdCBDaGF0SW5wdXQgPSBtZW1vKCh7IG9uU2VuZE1lc3NhZ2UsIGRpc2FibGVkLCBjaGFyYWN0ZXJOYW1lLCBpc1N0cmVhbWluZyB9OiBDaGF0SW5wdXRQcm9wcykgPT4ge1xyXG4gIGNvbnN0IFtuZXdNZXNzYWdlLCBzZXROZXdNZXNzYWdlXSA9IHVzZVN0YXRlKCcnKTtcclxuICBjb25zdCBpbnB1dFJlZiA9IHVzZVJlZjxIVE1MSW5wdXRFbGVtZW50PihudWxsKTtcclxuXHJcbiAgLy8gVXNlIHJlZiB0byBzdG9yZSBjdXJyZW50IG1lc3NhZ2UgdG8gYXZvaWQgc3RhbGUgY2xvc3VyZXNcclxuICBjb25zdCBtZXNzYWdlUmVmID0gdXNlUmVmKCcnKTtcclxuICBtZXNzYWdlUmVmLmN1cnJlbnQgPSBuZXdNZXNzYWdlO1xyXG5cclxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKCgpID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRNZXNzYWdlID0gbWVzc2FnZVJlZi5jdXJyZW50LnRyaW0oKTtcclxuICAgIGlmICghY3VycmVudE1lc3NhZ2UgfHwgZGlzYWJsZWQpIHJldHVybjtcclxuICAgIG9uU2VuZE1lc3NhZ2UoY3VycmVudE1lc3NhZ2UpO1xyXG4gICAgc2V0TmV3TWVzc2FnZSgnJyk7XHJcbiAgICBtZXNzYWdlUmVmLmN1cnJlbnQgPSAnJztcclxuICB9LCBbZGlzYWJsZWQsIG9uU2VuZE1lc3NhZ2VdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlS2V5UHJlc3MgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuS2V5Ym9hcmRFdmVudCkgPT4ge1xyXG4gICAgaWYgKGUua2V5ID09PSAnRW50ZXInICYmICFlLnNoaWZ0S2V5KSB7XHJcbiAgICAgIGUucHJldmVudERlZmF1bHQoKTtcclxuICAgICAgaGFuZGxlU2VuZE1lc3NhZ2UoKTtcclxuICAgIH1cclxuICB9LCBbaGFuZGxlU2VuZE1lc3NhZ2VdKTtcclxuXHJcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuQ2hhbmdlRXZlbnQ8SFRNTElucHV0RWxlbWVudD4pID0+IHtcclxuICAgIGNvbnN0IHZhbHVlID0gZS50YXJnZXQudmFsdWU7XHJcbiAgICBzZXROZXdNZXNzYWdlKHZhbHVlKTtcclxuICAgIG1lc3NhZ2VSZWYuY3VycmVudCA9IHZhbHVlO1xyXG4gIH0sIFtdKTtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMCBib3JkZXItdCBwLTQgYmctYmFja2dyb3VuZC85NSBiYWNrZHJvcC1ibHVyIHN1cHBvcnRzLVtiYWNrZHJvcC1maWx0ZXJdOmJnLWJhY2tncm91bmQvNjBcIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWVuZCBzcGFjZS14LTNcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSByZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgIHJlZj17aW5wdXRSZWZ9XHJcbiAgICAgICAgICAgIHZhbHVlPXtuZXdNZXNzYWdlfVxyXG4gICAgICAgICAgICBvbkNoYW5nZT17aGFuZGxlSW5wdXRDaGFuZ2V9XHJcbiAgICAgICAgICAgIG9uS2V5UHJlc3M9e2hhbmRsZUtleVByZXNzfVxyXG4gICAgICAgICAgICBwbGFjZWhvbGRlcj17YE1lc3NhZ2UgJHtjaGFyYWN0ZXJOYW1lIHx8ICdjaGFyYWN0ZXInfS4uLmB9XHJcbiAgICAgICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZH1cclxuICAgICAgICAgICAgY2xhc3NOYW1lPVwicHItMTIgcHktMyByb3VuZGVkLTJ4bCBib3JkZXItMiBmb2N1czpib3JkZXItWyMyREQ0QkZdIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgIC8+XHJcbiAgICAgICAgICB7LyogQ2hhcmFjdGVyIGNvdW50IG9yIHN0YXR1cyAqL31cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgcmlnaHQtMyB0b3AtMS8yIHRyYW5zZm9ybSAtdHJhbnNsYXRlLXktMS8yXCI+XHJcbiAgICAgICAgICAgIHtkaXNhYmxlZCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTQgaC00IGJvcmRlci0yIGJvcmRlci1bIzJERDRCRl0gYm9yZGVyLXQtdHJhbnNwYXJlbnQgcm91bmRlZC1mdWxsIGFuaW1hdGUtc3BpblwiPjwvZGl2PlxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9kaXY+XHJcblxyXG4gICAgICAgIDxCdXR0b25cclxuICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNlbmRNZXNzYWdlfVxyXG4gICAgICAgICAgZGlzYWJsZWQ9eyFuZXdNZXNzYWdlLnRyaW0oKSB8fCBkaXNhYmxlZH1cclxuICAgICAgICAgIGNsYXNzTmFtZT1cImJnLVsjMkRENEJGXSBob3ZlcjpiZy1bIzE0QjhBNl0gdGV4dC13aGl0ZSByb3VuZGVkLTJ4bCBweC02IHB5LTMgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMjAwIGhvdmVyOnNjYWxlLTEwNSBkaXNhYmxlZDpob3ZlcjpzY2FsZS0xMDBcIlxyXG4gICAgICAgID5cclxuICAgICAgICAgIDxTZW5kIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHsvKiBRdWljayBhY3Rpb25zIG9yIHN1Z2dlc3Rpb25zICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBtdC0yIHRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgPHNwYW4+UHJlc3MgRW50ZXIgdG8gc2VuZCwgU2hpZnQrRW50ZXIgZm9yIG5ldyBsaW5lPC9zcGFuPlxyXG4gICAgICAgIHtpc1N0cmVhbWluZyAmJiAoXHJcbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LVsjMkRENEJGXSBhbmltYXRlLXB1bHNlXCI+QUkgaXMgcmVzcG9uZGluZy4uLjwvc3Bhbj5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuY29uc3QgQ2hhdEludGVyZmFjZSA9IG1lbW8oKHsgY2hhdCwgb25CYWNrLCBvblRvZ2dsZVByb2ZpbGUgfTogQ2hhdEludGVyZmFjZVByb3BzKSA9PiB7XHJcbiAgY29uc3QgW21lc3NhZ2VzLCBzZXRNZXNzYWdlc10gPSB1c2VTdGF0ZTxNZXNzYWdlW10+KFtdKTtcclxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2VuZGluZywgc2V0U2VuZGluZ10gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW3N0cmVhbWluZ01lc3NhZ2UsIHNldFN0cmVhbWluZ01lc3NhZ2VdID0gdXNlU3RhdGUoJycpO1xyXG4gIGNvbnN0IFtpc1N0cmVhbWluZywgc2V0SXNTdHJlYW1pbmddID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IFtpc01vYmlsZSwgc2V0SXNNb2JpbGVdID0gdXNlU3RhdGUoZmFsc2UpO1xyXG4gIGNvbnN0IG1lc3NhZ2VzRW5kUmVmID0gdXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcclxuICBjb25zdCBzdHJlYW1Db25uZWN0aW9uUmVmID0gdXNlUmVmPHsgY2xvc2U6ICgpID0+IHZvaWQgfSB8IG51bGw+KG51bGwpO1xyXG4gIGNvbnN0IHN0cmVhbWluZ1RpbWVvdXRSZWYgPSB1c2VSZWY8Tm9kZUpTLlRpbWVvdXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBzdHJlYW1UaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XHJcblxyXG4gIC8vIFN0YWJsZSByZWZlcmVuY2VzIGZvciBjaGFyYWN0ZXIgZGF0YSB0byBwcmV2ZW50IHVubmVjZXNzYXJ5IHJlLXJlbmRlcnNcclxuICBjb25zdCBjaGFyYWN0ZXJJbWFnZVJlZiA9IHVzZVJlZihjaGF0LmNoYXJhY3Rlcj8uaW1hZ2UpO1xyXG4gIGNvbnN0IGNoYXJhY3Rlck5hbWVSZWYgPSB1c2VSZWYoY2hhdC5jaGFyYWN0ZXI/Lm5hbWUpO1xyXG5cclxuICAvLyBEZXRlY3QgbW9iaWxlIHNjcmVlbiBzaXplXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrTW9iaWxlID0gKCkgPT4ge1xyXG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNoZWNrTW9iaWxlKCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBVcGRhdGUgcmVmcyB3aGVuIGNoYXQgY2hhbmdlc1xyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBjaGFyYWN0ZXJJbWFnZVJlZi5jdXJyZW50ID0gY2hhdC5jaGFyYWN0ZXI/LmltYWdlO1xyXG4gICAgY2hhcmFjdGVyTmFtZVJlZi5jdXJyZW50ID0gY2hhdC5jaGFyYWN0ZXI/Lm5hbWU7XHJcbiAgfSwgW2NoYXQuY2hhcmFjdGVyPy5pbWFnZSwgY2hhdC5jaGFyYWN0ZXI/Lm5hbWVdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGxvYWRNZXNzYWdlcygpO1xyXG4gICAgcmV0dXJuICgpID0+IHtcclxuICAgICAgLy8gQ2xlYW51cCBzdHJlYW0gY29ubmVjdGlvbiBvbiB1bm1vdW50XHJcbiAgICAgIGlmIChzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQuY2xvc2UoKTtcclxuICAgICAgfVxyXG4gICAgICAvLyBDbGVhbnVwIHN0cmVhbWluZyB0aW1lb3V0XHJcbiAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgICAvLyBDbGVhbnVwIHN0cmVhbSB0aW1lb3V0XHJcbiAgICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuICB9LCBbY2hhdC5pZF0pO1xyXG5cclxuICAvLyBTdGFibGUgc2Nyb2xsIGZ1bmN0aW9uIHRoYXQgZG9lc24ndCBjYXVzZSByZS1yZW5kZXJzXHJcbiAgY29uc3Qgc2Nyb2xsVG9Cb3R0b21SZWYgPSB1c2VSZWY8KCkgPT4gdm9pZD4oKCkgPT4ge30pO1xyXG4gIHNjcm9sbFRvQm90dG9tUmVmLmN1cnJlbnQgPSAoKSA9PiB7XHJcbiAgICBtZXNzYWdlc0VuZFJlZi5jdXJyZW50Py5zY3JvbGxJbnRvVmlldyh7IGJlaGF2aW9yOiAnc21vb3RoJyB9KTtcclxuICB9O1xyXG5cclxuICAvLyBPbmx5IHNjcm9sbCB3aGVuIG1lc3NhZ2VzIGNvdW50IGNoYW5nZXMsIG5vdCBvbiBldmVyeSByZW5kZXJcclxuICBjb25zdCBtZXNzYWdlc0NvdW50UmVmID0gdXNlUmVmKG1lc3NhZ2VzLmxlbmd0aCk7XHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChtZXNzYWdlcy5sZW5ndGggPiBtZXNzYWdlc0NvdW50UmVmLmN1cnJlbnQpIHtcclxuICAgICAgbWVzc2FnZXNDb3VudFJlZi5jdXJyZW50ID0gbWVzc2FnZXMubGVuZ3RoO1xyXG4gICAgICBjb25zdCB0aW1lb3V0SWQgPSBzZXRUaW1lb3V0KCgpID0+IHtcclxuICAgICAgICBzY3JvbGxUb0JvdHRvbVJlZi5jdXJyZW50Py4oKTtcclxuICAgICAgfSwgMTAwKTtcclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgfVxyXG4gICAgbWVzc2FnZXNDb3VudFJlZi5jdXJyZW50ID0gbWVzc2FnZXMubGVuZ3RoO1xyXG4gIH0sIFttZXNzYWdlcy5sZW5ndGhdKTtcclxuXHJcbiAgLy8gU2VwYXJhdGUgZWZmZWN0IGZvciBzdHJlYW1pbmcgLSBvbmx5IHNjcm9sbCB3aGVuIHN0cmVhbWluZyBzdGFydHMgb3IgZW5kc1xyXG4gIGNvbnN0IGlzU3RyZWFtaW5nUmVmID0gdXNlUmVmKGlzU3RyZWFtaW5nKTtcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgaWYgKGlzU3RyZWFtaW5nICYmICFpc1N0cmVhbWluZ1JlZi5jdXJyZW50KSB7XHJcbiAgICAgIC8vIFN0cmVhbWluZyBqdXN0IHN0YXJ0ZWRcclxuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgc2Nyb2xsVG9Cb3R0b21SZWYuY3VycmVudD8uKCk7XHJcbiAgICAgIH0sIDIwMCk7XHJcbiAgICAgIGlzU3RyZWFtaW5nUmVmLmN1cnJlbnQgPSBpc1N0cmVhbWluZztcclxuICAgICAgcmV0dXJuICgpID0+IGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xyXG4gICAgfVxyXG4gICAgaXNTdHJlYW1pbmdSZWYuY3VycmVudCA9IGlzU3RyZWFtaW5nO1xyXG4gIH0sIFtpc1N0cmVhbWluZ10pO1xyXG5cclxuICBjb25zdCBsb2FkTWVzc2FnZXMgPSBhc3luYyAoKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBzZXRMb2FkaW5nKHRydWUpO1xyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGNoYXRTZXJ2aWNlLmdldENoYXRNZXNzYWdlcyhjaGF0LmlkLCB7IGxpbWl0OiA1MCB9KTtcclxuICAgICAgc2V0TWVzc2FnZXMocmVzcG9uc2UuZGF0YS5yZXZlcnNlKCkpOyAvLyBSZXZlcnNlIHRvIHNob3cgb2xkZXN0IGZpcnN0XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gbG9hZCBtZXNzYWdlczonLCBlcnJvcik7XHJcbiAgICB9IGZpbmFsbHkge1xyXG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVTZW5kTWVzc2FnZSA9IHVzZUNhbGxiYWNrKGFzeW5jIChtZXNzYWdlVGV4dDogc3RyaW5nKSA9PiB7XHJcbiAgICBpZiAoIW1lc3NhZ2VUZXh0LnRyaW0oKSB8fCBzZW5kaW5nKSByZXR1cm47XHJcblxyXG4gICAgY29uc3QgdGVtcElkID0gYHRlbXAtJHtEYXRlLm5vdygpfWA7XHJcbiAgICBzZXRTZW5kaW5nKHRydWUpO1xyXG5cclxuICAgIHRyeSB7XHJcbiAgICAgIC8vIEFkZCB1c2VyIG1lc3NhZ2UgdG8gVUkgaW1tZWRpYXRlbHlcclxuICAgICAgY29uc3QgdXNlck1lc3NhZ2U6IE1lc3NhZ2UgPSB7XHJcbiAgICAgICAgaWQ6IHRlbXBJZCxcclxuICAgICAgICByb2xlOiAndXNlcicsXHJcbiAgICAgICAgY29udGVudDogbWVzc2FnZVRleHQsXHJcbiAgICAgICAgY29udGVudFR5cGU6ICd0ZXh0JyxcclxuICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICB1cGRhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgfTtcclxuXHJcbiAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIHVzZXJNZXNzYWdlXSk7XHJcblxyXG4gICAgICBjb25zb2xlLmxvZygnU2VuZGluZyBtZXNzYWdlIHRvIGNoYXQ6JywgY2hhdC5pZCwgeyBtZXNzYWdlOiBtZXNzYWdlVGV4dCwgc3RyZWFtaW5nOiB0cnVlIH0pO1xyXG5cclxuICAgICAgLy8gU2VuZCBtZXNzYWdlIHdpdGggc3RyZWFtaW5nIGVuYWJsZWRcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBjaGF0U2VydmljZS5zZW5kTWVzc2FnZShjaGF0LmlkLCB7XHJcbiAgICAgICAgbWVzc2FnZTogbWVzc2FnZVRleHQsXHJcbiAgICAgICAgc3RyZWFtaW5nOiB0cnVlLFxyXG4gICAgICB9KTtcclxuXHJcbiAgICAgIGNvbnNvbGUubG9nKCdNZXNzYWdlIHNlbnQgc3VjY2Vzc2Z1bGx5OicsIHJlc3BvbnNlKTtcclxuXHJcbiAgICAgIC8vIFVwZGF0ZSB0aGUgdGVtcG9yYXJ5IG1lc3NhZ2Ugd2l0aCByZWFsIElEIGlmIGF2YWlsYWJsZVxyXG4gICAgICBpZiAocmVzcG9uc2UuaWQpIHtcclxuICAgICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYubWFwKG1zZyA9PlxyXG4gICAgICAgICAgbXNnLmlkID09PSB0ZW1wSWQgPyB7IC4uLm1zZywgaWQ6IHJlc3BvbnNlLmlkIH0gOiBtc2dcclxuICAgICAgICApKTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gU3RhcnQgc3RyZWFtaW5nIHJlc3BvbnNlXHJcbiAgICAgIGF3YWl0IHN0YXJ0U3RyZWFtaW5nKCk7XHJcblxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHNlbmQgbWVzc2FnZTonLCBlcnJvcik7XHJcbiAgICAgIC8vIFJlbW92ZSB0aGUgdGVtcG9yYXJ5IHVzZXIgbWVzc2FnZSBvbiBlcnJvclxyXG4gICAgICBzZXRNZXNzYWdlcyhwcmV2ID0+IHByZXYuZmlsdGVyKG1zZyA9PiBtc2cuaWQgIT09IHRlbXBJZCkpO1xyXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIHNlbmQgbWVzc2FnZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldFNlbmRpbmcoZmFsc2UpO1xyXG4gICAgfVxyXG4gIH0sIFtzZW5kaW5nLCBjaGF0LmlkXSk7XHJcblxyXG4gIGNvbnN0IHN0YXJ0U3RyZWFtaW5nID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgY29uc29sZS5sb2coJ1N0YXJ0aW5nIHN0cmVhbSBmb3IgY2hhdDonLCBjaGF0LmlkKTtcclxuICAgIHNldElzU3RyZWFtaW5nKHRydWUpO1xyXG4gICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcblxyXG4gICAgLy8gQ2xvc2UgZXhpc3RpbmcgY29ubmVjdGlvbiBhbmQgY2xlYXIgdGltZW91dHNcclxuICAgIGlmIChzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQpIHtcclxuICAgICAgc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50LmNsb3NlKCk7XHJcbiAgICB9XHJcbiAgICBpZiAoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgfVxyXG4gICAgaWYgKHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgIH1cclxuXHJcbiAgICBsZXQgY3VycmVudFN0cmVhbWluZ01lc3NhZ2UgPSAnJztcclxuXHJcbiAgICAvLyBNb3JlIGFnZ3Jlc3NpdmUgdGhyb3R0bGluZyBmb3Igc3RyZWFtaW5nIG1lc3NhZ2UgdXBkYXRlc1xyXG4gICAgbGV0IGxhc3RVcGRhdGVUaW1lID0gMDtcclxuICAgIGNvbnN0IHVwZGF0ZVN0cmVhbWluZ01lc3NhZ2UgPSAobWVzc2FnZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgLy8gSW1tZWRpYXRlIHVwZGF0ZSBpZiBpdCdzIGJlZW4gbW9yZSB0aGFuIDEwMG1zIHNpbmNlIGxhc3QgdXBkYXRlXHJcbiAgICAgIGlmIChub3cgLSBsYXN0VXBkYXRlVGltZSA+IDEwMCkge1xyXG4gICAgICAgIHNldFN0cmVhbWluZ01lc3NhZ2UobWVzc2FnZSk7XHJcbiAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBub3c7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgLy8gT3RoZXJ3aXNlLCB0aHJvdHRsZSB0aGUgdXBkYXRlXHJcbiAgICAgICAgc3RyZWFtaW5nVGltZW91dFJlZi5jdXJyZW50ID0gc2V0VGltZW91dCgoKSA9PiB7XHJcbiAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKG1lc3NhZ2UpO1xyXG4gICAgICAgICAgbGFzdFVwZGF0ZVRpbWUgPSBEYXRlLm5vdygpO1xyXG4gICAgICAgIH0sIDEwMCk7XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgY29uc3Qgb25NZXNzYWdlID0gKGRhdGE6IFNTRUV2ZW50KSA9PiB7XHJcbiAgICAgIGNvbnNvbGUubG9nKCdSZWNlaXZlZCBTU0UgZXZlbnQ6JywgZGF0YSk7XHJcblxyXG4gICAgICBzd2l0Y2ggKGRhdGEuZXZlbnQpIHtcclxuICAgICAgICBjYXNlICdzdGFydCc6XHJcbiAgICAgICAgICBjb25zb2xlLmxvZygnU3RyZWFtIHN0YXJ0ZWQnKTtcclxuICAgICAgICAgIGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlID0gJyc7XHJcbiAgICAgICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTtcclxuICAgICAgICAgIGJyZWFrO1xyXG4gICAgICAgIGNhc2UgJ3Rva2VuJzpcclxuICAgICAgICAgIGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlICs9IGRhdGEuZGF0YTtcclxuICAgICAgICAgIHVwZGF0ZVN0cmVhbWluZ01lc3NhZ2UoY3VycmVudFN0cmVhbWluZ01lc3NhZ2UpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSAnbWV0YWRhdGEnOlxyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSBtZXRhZGF0YTonLCBkYXRhLmRhdGEpO1xyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgY2FzZSAnZW5kJzpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdTdHJlYW0gZW5kZWQsIGZpbmFsIG1lc3NhZ2U6JywgY3VycmVudFN0cmVhbWluZ01lc3NhZ2UpO1xyXG4gICAgICAgICAgLy8gQ2xlYXIgYW55IHBlbmRpbmcgc3RyZWFtaW5nIHVwZGF0ZXNcclxuICAgICAgICAgIGlmIChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICAgICAgY2xlYXJUaW1lb3V0KHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCk7XHJcbiAgICAgICAgICB9XHJcbiAgICAgICAgICAvLyBDbGVhciBzdHJlYW0gdGltZW91dCB0byBwcmV2ZW50IGZhbHNlIHRpbWVvdXQgZXJyb3JzXHJcbiAgICAgICAgICBpZiAoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgLy8gRmluYWxpemUgdGhlIHN0cmVhbWluZyBtZXNzYWdlXHJcbiAgICAgICAgICBjb25zdCBmaW5hbE1lc3NhZ2U6IE1lc3NhZ2UgPSB7XHJcbiAgICAgICAgICAgIGlkOiBkYXRhLmRhdGE/LmNoYXRNZXNzYWdlSWQgfHwgYG1zZy0ke0RhdGUubm93KCl9YCxcclxuICAgICAgICAgICAgcm9sZTogJ2Fzc2lzdGFudCcsXHJcbiAgICAgICAgICAgIGNvbnRlbnQ6IGN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlLFxyXG4gICAgICAgICAgICBjb250ZW50VHlwZTogJ3RleHQnLFxyXG4gICAgICAgICAgICBjcmVhdGVkQXQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcclxuICAgICAgICAgICAgdXBkYXRlZEF0OiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksXHJcbiAgICAgICAgICB9O1xyXG5cclxuICAgICAgICAgIHNldE1lc3NhZ2VzKHByZXYgPT4gWy4uLnByZXYsIGZpbmFsTWVzc2FnZV0pO1xyXG4gICAgICAgICAgc2V0U3RyZWFtaW5nTWVzc2FnZSgnJyk7XHJcbiAgICAgICAgICBzZXRJc1N0cmVhbWluZyhmYWxzZSk7XHJcbiAgICAgICAgICBpZiAoc3RyZWFtQ29ubmVjdGlvblJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgYnJlYWs7XHJcbiAgICAgICAgZGVmYXVsdDpcclxuICAgICAgICAgIGNvbnNvbGUubG9nKCdVbmtub3duIFNTRSBldmVudDonLCBkYXRhLmV2ZW50KTtcclxuICAgICAgfVxyXG4gICAgfTtcclxuXHJcbiAgICBjb25zdCBvbkVycm9yID0gYXN5bmMgKGVycm9yOiBhbnkpID0+IHtcclxuICAgICAgY29uc29sZS5lcnJvcignU3RyZWFtIEVycm9yOicsIGVycm9yKTtcclxuXHJcbiAgICAgIC8vIENsZWFyIHRpbWVvdXRzIGZpcnN0IHRvIHByZXZlbnQgZnVydGhlciBlcnJvcnNcclxuICAgICAgaWYgKHN0cmVhbWluZ1RpbWVvdXRSZWYuY3VycmVudCkge1xyXG4gICAgICAgIGNsZWFyVGltZW91dChzdHJlYW1pbmdUaW1lb3V0UmVmLmN1cnJlbnQpO1xyXG4gICAgICB9XHJcbiAgICAgIGlmIChzdHJlYW1UaW1lb3V0UmVmLmN1cnJlbnQpIHtcclxuICAgICAgICBjbGVhclRpbWVvdXQoc3RyZWFtVGltZW91dFJlZi5jdXJyZW50KTtcclxuICAgICAgfVxyXG5cclxuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICBzZXRTdHJlYW1pbmdNZXNzYWdlKCcnKTtcclxuICAgICAgaWYgKHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudC5jbG9zZSgpO1xyXG4gICAgICB9XHJcblxyXG4gICAgICAvLyBPbmx5IHJlbG9hZCBtZXNzYWdlcyBpZiBpdCdzIG5vdCBhIHRpbWVvdXQgZXJyb3IgYW5kIHN0cmVhbWluZyB3YXMgYWN0dWFsbHkgYWN0aXZlXHJcbiAgICAgIGlmICghZXJyb3IubWVzc2FnZT8uaW5jbHVkZXMoJ3RpbWVvdXQnKSAmJiBpc1N0cmVhbWluZykge1xyXG4gICAgICAgIGNvbnNvbGUubG9nKCdBdHRlbXB0aW5nIHRvIHJlbG9hZCBtZXNzYWdlcyBhcyBmYWxsYmFjay4uLicpO1xyXG4gICAgICAgIHRyeSB7XHJcbiAgICAgICAgICBhd2FpdCBsb2FkTWVzc2FnZXMoKTtcclxuICAgICAgICB9IGNhdGNoIChyZWxvYWRFcnJvcikge1xyXG4gICAgICAgICAgY29uc29sZS5lcnJvcignRmFpbGVkIHRvIHJlbG9hZCBtZXNzYWdlczonLCByZWxvYWRFcnJvcik7XHJcbiAgICAgICAgICAvLyBEb24ndCBzaG93IGFsZXJ0IGZvciB0aW1lb3V0IGVycm9ycyB0byBhdm9pZCBkaXNydXB0aW5nIHVzZXJcclxuICAgICAgICB9XHJcbiAgICAgIH1cclxuICAgIH07XHJcblxyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gQ3JlYXRlIG5ldyBzdHJlYW0gY29ubmVjdGlvblxyXG4gICAgICBjb25zdCBjb25uZWN0aW9uID0gYXdhaXQgY2hhdFNlcnZpY2UuY3JlYXRlU3RyZWFtQ29ubmVjdGlvbihjaGF0LmlkLCBvbk1lc3NhZ2UsIG9uRXJyb3IpO1xyXG4gICAgICBzdHJlYW1Db25uZWN0aW9uUmVmLmN1cnJlbnQgPSBjb25uZWN0aW9uO1xyXG5cclxuICAgICAgLy8gU2V0IHRpbWVvdXQgZm9yIHN0cmVhbWluZyAoMzAgc2Vjb25kcykgLSBidXQgc3RvcmUgcmVmZXJlbmNlIHRvIGNsZWFyIGl0XHJcbiAgICAgIHN0cmVhbVRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xyXG4gICAgICAgIC8vIE9ubHkgdHJpZ2dlciB0aW1lb3V0IGlmIHN0cmVhbWluZyBpcyBzdGlsbCBhY3RpdmUgYW5kIGNvbm5lY3Rpb24gZXhpc3RzXHJcbiAgICAgICAgaWYgKGlzU3RyZWFtaW5nICYmIHN0cmVhbUNvbm5lY3Rpb25SZWYuY3VycmVudCkge1xyXG4gICAgICAgICAgY29uc29sZS5sb2coJ1N0cmVhbSB0aW1lb3V0LCBmYWxsaW5nIGJhY2sgdG8gbWVzc2FnZSByZWxvYWQnKTtcclxuICAgICAgICAgIG9uRXJyb3IobmV3IEVycm9yKCdTdHJlYW0gdGltZW91dCcpKTtcclxuICAgICAgICB9XHJcbiAgICAgIH0sIDMwMDAwKTtcclxuXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdGYWlsZWQgdG8gY3JlYXRlIHN0cmVhbSBjb25uZWN0aW9uOicsIGVycm9yKTtcclxuICAgICAgc2V0SXNTdHJlYW1pbmcoZmFsc2UpO1xyXG4gICAgICBhbGVydCgnRmFpbGVkIHRvIGVzdGFibGlzaCBjb25uZWN0aW9uIGZvciBBSSByZXNwb25zZS4gUGxlYXNlIHRyeSBhZ2Fpbi4nKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBpZiAobG9hZGluZykge1xyXG4gICAgcmV0dXJuIChcclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGgtZnVsbCBvdmVyZmxvdy1oaWRkZW5cIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTAgYm9yZGVyLWIgcC00IGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgc3BhY2UteC0zXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0xMCBoLTEwIGJnLW11dGVkIHJvdW5kZWQtZnVsbFwiIC8+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTMyXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtMyBiZy1tdXRlZCByb3VuZGVkIHctMjBcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIHAtNCBzcGFjZS15LTQgb3ZlcmZsb3cteS1hdXRvXCI+XHJcbiAgICAgICAgICB7QXJyYXkuZnJvbSh7IGxlbmd0aDogMyB9KS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgPGRpdiBrZXk9e2l9IGNsYXNzTmFtZT1cImZsZXggc3BhY2UteC0zIGFuaW1hdGUtcHVsc2VcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctOCBoLTggYmctbXV0ZWQgcm91bmRlZC1mdWxsXCIgLz5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBzcGFjZS15LTJcIj5cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaC00IGJnLW11dGVkIHJvdW5kZWQgdy0zLzRcIiAvPlxyXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTQgYmctbXV0ZWQgcm91bmRlZCB3LTEvMlwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSl9XHJcbiAgICAgICAgPC9kaXY+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1mdWxsIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICB7LyogQ2hhdCBIZWFkZXIgLSBGaXhlZCAqL31cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIGJvcmRlci1iIHAtNCBiZy1iYWNrZ3JvdW5kLzk1IGJhY2tkcm9wLWJsdXIgc3VwcG9ydHMtW2JhY2tkcm9wLWZpbHRlcl06YmctYmFja2dyb3VuZC82MFwiPlxyXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtM1wiPlxyXG4gICAgICAgICAgICB7LyogTW9iaWxlIGJhY2sgYnV0dG9uICovfVxyXG4gICAgICAgICAgICB7aXNNb2JpbGUgJiYgb25CYWNrICYmIChcclxuICAgICAgICAgICAgICA8QnV0dG9uXHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiZ2hvc3RcIlxyXG4gICAgICAgICAgICAgICAgc2l6ZT1cImljb25cIlxyXG4gICAgICAgICAgICAgICAgb25DbGljaz17b25CYWNrfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaG92ZXI6YmctbXV0ZWQvNTAgbWQ6aGlkZGVuXCJcclxuICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9XCJCYWNrIHRvIGNoYXQgbGlzdFwiXHJcbiAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgPEFycm93TGVmdCBjbGFzc05hbWU9XCJ3LTUgaC01XCIgLz5cclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgKX1cclxuXHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2BmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTMgJHtcclxuICAgICAgICAgICAgICAgIGlzTW9iaWxlICYmIG9uVG9nZ2xlUHJvZmlsZSA/ICdjdXJzb3ItcG9pbnRlciBob3ZlcjpiZy1tdXRlZC81MCByb3VuZGVkLWxnIHAtMiAtbS0yIHRyYW5zaXRpb24tY29sb3JzJyA6ICcnXHJcbiAgICAgICAgICAgICAgfWB9XHJcbiAgICAgICAgICAgICAgb25DbGljaz17aXNNb2JpbGUgJiYgb25Ub2dnbGVQcm9maWxlID8gb25Ub2dnbGVQcm9maWxlIDogdW5kZWZpbmVkfVxyXG4gICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZVwiPlxyXG4gICAgICAgICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJ3LTEyIGgtMTIgcmluZy0yIHJpbmctWyMyREQ0QkZdLzIwXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxBdmF0YXJJbWFnZSBzcmM9e2NoYXQuY2hhcmFjdGVyPy5pbWFnZX0gYWx0PXtjaGF0LmNoYXJhY3Rlcj8ubmFtZX0gLz5cclxuICAgICAgICAgICAgICAgICAgPEF2YXRhckZhbGxiYWNrIGNsYXNzTmFtZT1cImJnLVsjMkRENEJGXS8xMCB0ZXh0LVsjMkRENEJGXSBmb250LXNlbWlib2xkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAge2NoYXQuY2hhcmFjdGVyPy5uYW1lPy5jaGFyQXQoMCk/LnRvVXBwZXJDYXNlKCkgfHwgJ0MnfVxyXG4gICAgICAgICAgICAgICAgICA8L0F2YXRhckZhbGxiYWNrPlxyXG4gICAgICAgICAgICAgICAgPC9BdmF0YXI+XHJcbiAgICAgICAgICAgICAgICB7LyogT25saW5lIGluZGljYXRvciAqL31cclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgYm90dG9tLTAgcmlnaHQtMCB3LTMgaC0zIGJnLWdyZWVuLTUwMCBib3JkZXItMiBib3JkZXItYmFja2dyb3VuZCByb3VuZGVkLWZ1bGxcIj48L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1sZ1wiPntjaGF0LmNoYXJhY3Rlcj8ubmFtZSB8fCAnVW5rbm93biBDaGFyYWN0ZXInfTwvaDI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPntjaGF0Lm1lc3NhZ2VDb3VudH0gbWVzc2FnZXM8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgIDxzcGFuPuKAojwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmVlbi01MDBcIj5PbmxpbmU8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTJcIj5cclxuICAgICAgICAgICAgey8qIE1vYmlsZSBwcm9maWxlIHRvZ2dsZSBidXR0b24gKi99XHJcbiAgICAgICAgICAgIHtpc01vYmlsZSAmJiBvblRvZ2dsZVByb2ZpbGUgJiYgKFxyXG4gICAgICAgICAgICAgIDxCdXR0b25cclxuICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJnaG9zdFwiXHJcbiAgICAgICAgICAgICAgICBzaXplPVwiaWNvblwiXHJcbiAgICAgICAgICAgICAgICBvbkNsaWNrPXtvblRvZ2dsZVByb2ZpbGV9XHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJob3ZlcjpiZy1tdXRlZC81MCBtZDpoaWRkZW5cIlxyXG4gICAgICAgICAgICAgICAgYXJpYS1sYWJlbD1cIlZpZXcgY2hhcmFjdGVyIHByb2ZpbGVcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIDxJbmZvIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPlxyXG4gICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICApfVxyXG5cclxuICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwiaWNvblwiIGNsYXNzTmFtZT1cImhvdmVyOmJnLW11dGVkLzUwXCI+XHJcbiAgICAgICAgICAgICAgPE1vcmVWZXJ0aWNhbCBjbGFzc05hbWU9XCJ3LTQgaC00XCIgLz5cclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWVzc2FnZXMgLSBTY3JvbGxhYmxlICovfVxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gb3ZlcmZsb3cteC1oaWRkZW4gcC00IHNwYWNlLXktNCBiZy1ncmFkaWVudC10by1iIGZyb20tYmFja2dyb3VuZCB0by1tdXRlZC8yMFwiPlxyXG4gICAgICAgIHttZXNzYWdlcy5tYXAoKG1lc3NhZ2UpID0+IChcclxuICAgICAgICAgIDxNZXNzYWdlSXRlbVxyXG4gICAgICAgICAgICBrZXk9e21lc3NhZ2UuaWR9XHJcbiAgICAgICAgICAgIG1lc3NhZ2U9e21lc3NhZ2V9XHJcbiAgICAgICAgICAgIGNoYXJhY3RlckltYWdlPXtjaGFyYWN0ZXJJbWFnZVJlZi5jdXJyZW50fVxyXG4gICAgICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICkpfVxyXG5cclxuICAgICAgICB7LyogU3RyZWFtaW5nIE1lc3NhZ2UgKi99XHJcbiAgICAgICAge2lzU3RyZWFtaW5nICYmIChcclxuICAgICAgICAgIDxTdHJlYW1pbmdNZXNzYWdlXHJcbiAgICAgICAgICAgIHN0cmVhbWluZ01lc3NhZ2U9e3N0cmVhbWluZ01lc3NhZ2V9XHJcbiAgICAgICAgICAgIGNoYXJhY3RlckltYWdlPXtjaGFyYWN0ZXJJbWFnZVJlZi5jdXJyZW50fVxyXG4gICAgICAgICAgICBjaGFyYWN0ZXJOYW1lPXtjaGFyYWN0ZXJOYW1lUmVmLmN1cnJlbnR9XHJcbiAgICAgICAgICAvPlxyXG4gICAgICAgICl9XHJcblxyXG4gICAgICAgIDxkaXYgcmVmPXttZXNzYWdlc0VuZFJlZn0gLz5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7LyogTWVzc2FnZSBJbnB1dCAtIEZpeGVkICovfVxyXG4gICAgICA8Q2hhdElucHV0XHJcbiAgICAgICAgb25TZW5kTWVzc2FnZT17aGFuZGxlU2VuZE1lc3NhZ2V9XHJcbiAgICAgICAgZGlzYWJsZWQ9e3NlbmRpbmcgfHwgaXNTdHJlYW1pbmd9XHJcbiAgICAgICAgY2hhcmFjdGVyTmFtZT17Y2hhcmFjdGVyTmFtZVJlZi5jdXJyZW50fVxyXG4gICAgICAgIGlzU3RyZWFtaW5nPXtpc1N0cmVhbWluZ31cclxuICAgICAgLz5cclxuICAgIDwvZGl2PlxyXG4gICk7XHJcbn0pO1xyXG5cclxuQ2hhdEludGVyZmFjZS5kaXNwbGF5TmFtZSA9ICdDaGF0SW50ZXJmYWNlJztcclxuXHJcbmV4cG9ydCB7IENoYXRJbnRlcmZhY2UgfTtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJ1c2VSZWYiLCJ1c2VDYWxsYmFjayIsIm1lbW8iLCJjaGF0U2VydmljZSIsIkJ1dHRvbiIsIklucHV0IiwiQXZhdGFyIiwiQXZhdGFyRmFsbGJhY2siLCJBdmF0YXJJbWFnZSIsIlNlbmQiLCJNb3JlVmVydGljYWwiLCJBcnJvd0xlZnQiLCJJbmZvIiwiZm9ybWF0RGlzdGFuY2VUb05vdyIsIlJlYWN0TWFya2Rvd24iLCJyZW1hcmtHZm0iLCJNZXNzYWdlQ29udGVudCIsImNvbnRlbnQiLCJyb2xlIiwicCIsImNsYXNzTmFtZSIsImRpdiIsInJlbWFya1BsdWdpbnMiLCJjb21wb25lbnRzIiwiY2hpbGRyZW4iLCJlbSIsInN0cm9uZyIsImNvZGUiLCJpc0lubGluZSIsInVsIiwib2wiLCJsaSIsImJsb2NrcXVvdGUiLCJNZXNzYWdlSXRlbSIsIm1lc3NhZ2UiLCJjaGFyYWN0ZXJJbWFnZSIsImNoYXJhY3Rlck5hbWUiLCJmb3JtYXRUaW1lIiwiZGF0ZVN0cmluZyIsIkRhdGUiLCJhZGRTdWZmaXgiLCJzcmMiLCJhbHQiLCJjaGFyQXQiLCJ0b1VwcGVyQ2FzZSIsImNyZWF0ZWRBdCIsIlN0cmVhbWluZ01lc3NhZ2UiLCJzdHJlYW1pbmdNZXNzYWdlIiwic3BhbiIsInN0eWxlIiwiYW5pbWF0aW9uRGVsYXkiLCJDaGF0SW5wdXQiLCJvblNlbmRNZXNzYWdlIiwiZGlzYWJsZWQiLCJpc1N0cmVhbWluZyIsIm5ld01lc3NhZ2UiLCJzZXROZXdNZXNzYWdlIiwiaW5wdXRSZWYiLCJtZXNzYWdlUmVmIiwiY3VycmVudCIsImhhbmRsZVNlbmRNZXNzYWdlIiwiY3VycmVudE1lc3NhZ2UiLCJ0cmltIiwiaGFuZGxlS2V5UHJlc3MiLCJlIiwia2V5Iiwic2hpZnRLZXkiLCJwcmV2ZW50RGVmYXVsdCIsImhhbmRsZUlucHV0Q2hhbmdlIiwidmFsdWUiLCJ0YXJnZXQiLCJyZWYiLCJvbkNoYW5nZSIsIm9uS2V5UHJlc3MiLCJwbGFjZWhvbGRlciIsIm9uQ2xpY2siLCJDaGF0SW50ZXJmYWNlIiwiY2hhdCIsIm9uQmFjayIsIm9uVG9nZ2xlUHJvZmlsZSIsIm1lc3NhZ2VzIiwic2V0TWVzc2FnZXMiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNlbmRpbmciLCJzZXRTZW5kaW5nIiwic2V0U3RyZWFtaW5nTWVzc2FnZSIsInNldElzU3RyZWFtaW5nIiwiaXNNb2JpbGUiLCJzZXRJc01vYmlsZSIsIm1lc3NhZ2VzRW5kUmVmIiwic3RyZWFtQ29ubmVjdGlvblJlZiIsInN0cmVhbWluZ1RpbWVvdXRSZWYiLCJzdHJlYW1UaW1lb3V0UmVmIiwiY2hhcmFjdGVySW1hZ2VSZWYiLCJjaGFyYWN0ZXIiLCJpbWFnZSIsImNoYXJhY3Rlck5hbWVSZWYiLCJuYW1lIiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJsb2FkTWVzc2FnZXMiLCJjbG9zZSIsImNsZWFyVGltZW91dCIsImlkIiwic2Nyb2xsVG9Cb3R0b21SZWYiLCJzY3JvbGxJbnRvVmlldyIsImJlaGF2aW9yIiwibWVzc2FnZXNDb3VudFJlZiIsImxlbmd0aCIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJpc1N0cmVhbWluZ1JlZiIsInJlc3BvbnNlIiwiZ2V0Q2hhdE1lc3NhZ2VzIiwibGltaXQiLCJkYXRhIiwicmV2ZXJzZSIsImVycm9yIiwiY29uc29sZSIsIm1lc3NhZ2VUZXh0IiwidGVtcElkIiwibm93IiwidXNlck1lc3NhZ2UiLCJjb250ZW50VHlwZSIsInRvSVNPU3RyaW5nIiwidXBkYXRlZEF0IiwicHJldiIsImxvZyIsInN0cmVhbWluZyIsInNlbmRNZXNzYWdlIiwibWFwIiwibXNnIiwic3RhcnRTdHJlYW1pbmciLCJmaWx0ZXIiLCJhbGVydCIsImN1cnJlbnRTdHJlYW1pbmdNZXNzYWdlIiwibGFzdFVwZGF0ZVRpbWUiLCJ1cGRhdGVTdHJlYW1pbmdNZXNzYWdlIiwib25NZXNzYWdlIiwiZXZlbnQiLCJmaW5hbE1lc3NhZ2UiLCJjaGF0TWVzc2FnZUlkIiwib25FcnJvciIsImluY2x1ZGVzIiwicmVsb2FkRXJyb3IiLCJjb25uZWN0aW9uIiwiY3JlYXRlU3RyZWFtQ29ubmVjdGlvbiIsIkVycm9yIiwiQXJyYXkiLCJmcm9tIiwiXyIsImkiLCJ2YXJpYW50Iiwic2l6ZSIsImFyaWEtbGFiZWwiLCJ1bmRlZmluZWQiLCJoMiIsIm1lc3NhZ2VDb3VudCIsImRpc3BsYXlOYW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
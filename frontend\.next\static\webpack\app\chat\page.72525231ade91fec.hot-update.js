"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\")), \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\");\n_c2 = MessageItem;\nfunction ChatInterface(param) {\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt1, _chat_character_name1, _chat_character6, _chat_character7;\n    _s1();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    const scrollToBottom = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[scrollToBottom]\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"ChatInterface.useCallback[scrollToBottom]\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            // Only scroll when messages change, not on every render\n            const timeoutId = setTimeout(scrollToBottom, 100);\n            return ({\n                \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length,\n        streamingMessage,\n        scrollToBottom\n    ]);\n    const loadMessages = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[loadMessages]\": async ()=>{\n            try {\n                setLoading(true);\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                    limit: 50\n                });\n                setMessages(response.data.reverse()); // Reverse to show oldest first\n            } catch (error) {\n                console.error('Failed to load messages:', error);\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[loadMessages]\"], [\n        chat.id\n    ]);\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim() || sending) return;\n        const messageText = newMessage.trim();\n        const tempId = \"temp-\".concat(Date.now());\n        setNewMessage('');\n        setSending(true);\n        try {\n            // Add user message to UI immediately\n            const userMessage = {\n                id: tempId,\n                role: 'user',\n                content: messageText,\n                contentType: 'text',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            console.log('Sending message to chat:', chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            // Send message with streaming enabled\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            console.log('Message sent successfully:', response);\n            // Update the temporary message with real ID if available\n            if (response.id) {\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempId ? {\n                            ...msg,\n                            id: response.id\n                        } : msg));\n            }\n            // Start streaming response\n            await startStreaming();\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            // Remove the temporary user message on error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== tempId));\n            alert('Failed to send message. Please try again.');\n        } finally{\n            setSending(false);\n        }\n    };\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    setStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    const formatTime = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            return '';\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 346,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 348,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 349,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 345,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 359,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 343,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 382,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 385,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 388,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 386,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 373,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 394,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 372,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 371,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message, index)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                            children: [\n                                message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"w-8 h-8 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                            alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                            children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                            content: message.content,\n                                            role: message.role\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: formatTime(message.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 429,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 434,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 426,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 404,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-2 justify-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"w-8 h-8 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 447,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name1 = _chat_character6.name) === null || _chat_character_name1 === void 0 ? void 0 : (_chat_character_name_charAt1 = _chat_character_name1.charAt(0)) === null || _chat_character_name_charAt1 === void 0 ? void 0 : _chat_character_name_charAt1.toUpperCase()) || 'C'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 446,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                                children: [\n                                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                        content: streamingMessage,\n                                        role: \"assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Thinking\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 462,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 459,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 469,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 470,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 477,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 402,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        value: newMessage,\n                                        onChange: (e)=>setNewMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Message \".concat(((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'character', \"...\"),\n                                        disabled: sending || isStreaming,\n                                        className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                        children: (sending || isStreaming) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 495,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !newMessage.trim() || sending || isStreaming,\n                                className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 482,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#2DD4BF] animate-pulse\",\n                                children: \"AI is responding...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 481,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 369,\n        columnNumber: 5\n    }, this);\n}\n_s1(ChatInterface, \"vZGRU4u4Fathr6xVG6KVnr+Sw4o=\");\n_c3 = ChatInterface;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem$memo\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
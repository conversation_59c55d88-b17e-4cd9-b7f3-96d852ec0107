"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\")), \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\");\n_c2 = MessageItem;\nfunction ChatInterface(param) {\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt1, _chat_character_name1, _chat_character6, _chat_character7;\n    _s1();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim() || sending) return;\n        const messageText = newMessage.trim();\n        const tempId = \"temp-\".concat(Date.now());\n        setNewMessage('');\n        setSending(true);\n        try {\n            // Add user message to UI immediately\n            const userMessage = {\n                id: tempId,\n                role: 'user',\n                content: messageText,\n                contentType: 'text',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            console.log('Sending message to chat:', chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            // Send message with streaming enabled\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            console.log('Message sent successfully:', response);\n            // Update the temporary message with real ID if available\n            if (response.id) {\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempId ? {\n                            ...msg,\n                            id: response.id\n                        } : msg));\n            }\n            // Start streaming response\n            await startStreaming();\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            // Remove the temporary user message on error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== tempId));\n            alert('Failed to send message. Please try again.');\n        } finally{\n            setSending(false);\n        }\n    };\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    setStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    const formatTime = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            return '';\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 346,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 347,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 345,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 342,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 354,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 355,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 353,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 341,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 386,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 387,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 384,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 370,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message, index)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                            children: [\n                                message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"w-8 h-8 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                            alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                            children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                            content: message.content,\n                                            role: message.role\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: formatTime(message.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 427,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 424,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 416,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 402,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-2 justify-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"w-8 h-8 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name1 = _chat_character6.name) === null || _chat_character_name1 === void 0 ? void 0 : (_chat_character_name_charAt1 = _chat_character_name1.charAt(0)) === null || _chat_character_name_charAt1 === void 0 ? void 0 : _chat_character_name_charAt1.toUpperCase()) || 'C'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 446,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                                children: [\n                                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                        content: streamingMessage,\n                                        role: \"assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Thinking\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 457,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 443,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 475,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 400,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        value: newMessage,\n                                        onChange: (e)=>setNewMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Message \".concat(((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'character', \"...\"),\n                                        disabled: sending || isStreaming,\n                                        className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                        children: (sending || isStreaming) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 493,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 481,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !newMessage.trim() || sending || isStreaming,\n                                className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 503,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 480,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 11\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#2DD4BF] animate-pulse\",\n                                children: \"AI is responding...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 479,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 367,\n        columnNumber: 5\n    }, this);\n}\n_s1(ChatInterface, \"HWCezY2ns3dsLvFKUIeDCj1R1D4=\");\n_c3 = ChatInterface;\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem$memo\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
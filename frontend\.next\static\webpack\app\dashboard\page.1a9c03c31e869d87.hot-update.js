"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/castle.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sword.js\");\n/* harmony import */ var _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Castle,Heart,Home,MessageCircle,Rocket,Settings,Sword!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _components_nav_user__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/nav-user */ \"(app-pages-browser)/./src/components/nav-user.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst data = {\n    navMain: [\n        {\n            title: \"Eksplor\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            description: \"Jelajahi karakter AI\"\n        },\n        {\n            title: \"Chat Saya\",\n            url: \"/chat\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            description: \"Lanjutkan percakapan\"\n        },\n        {\n            title: \"Favorit\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            description: \"Karakter tersimpan\"\n        },\n        {\n            title: \"Pengaturan\",\n            url: \"/settings\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            description: \"Akun & preferensi\"\n        }\n    ],\n    quickActions: [\n        {\n            name: \"Fantasi\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"from-purple-500 to-pink-500\"\n        },\n        {\n            name: \"Romantis\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"from-pink-500 to-rose-500\"\n        },\n        {\n            name: \"Petualangan\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            color: \"from-orange-500 to-red-500\"\n        },\n        {\n            name: \"Sci-Fi\",\n            url: \"#\",\n            icon: _barrel_optimize_names_Castle_Heart_Home_MessageCircle_Rocket_Settings_Sword_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n            color: \"from-blue-500 to-cyan-500\"\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { ...props } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.Sidebar, {\n        variant: \"inset\",\n        ...props,\n        className: \"border-r-0\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarHeader, {\n                className: \"border-b-0 p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                    href: \"/dashboard\",\n                    className: \"group flex items-center justify-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        src: theme === 'dark' ? '/logowhite.png' : '/logoblack.png',\n                        alt: \"Bestieku Logo\",\n                        width: 120,\n                        height: 40,\n                        className: \"transition-all duration-300 group-hover:scale-105\",\n                        priority: true\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 86,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarContent, {\n                className: \"px-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3 mb-3\",\n                                children: \"Navigasi\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 102,\n                                columnNumber: 11\n                            }, this),\n                            data.navMain.map((item)=>{\n                                const isActive = pathname === item.url;\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: item.url,\n                                        className: \"flex items-center gap-3 px-3 h-12 rounded-xl transition-all duration-200 hover:bg-gradient-to-r hover:from-[#2DD4BF]/10 hover:to-[#14B8A6]/10 hover:scale-[1.02] group \".concat(isActive ? 'bg-gradient-to-r from-[#2DD4BF]/20 to-[#14B8A6]/20 border border-[#2DD4BF]/30' : ''),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-2 rounded-lg \".concat(isActive ? 'bg-[#2DD4BF] text-white shadow-md' : 'bg-muted/50 text-muted-foreground group-hover:bg-[#2DD4BF]/20 group-hover:text-[#2DD4BF]', \" transition-all duration-200\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"size-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium \".concat(isActive ? 'text-[#2DD4BF]' : ''),\n                                                        children: item.title\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 123,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                        lineNumber: 126,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 17\n                                    }, this)\n                                }, item.title, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-xs font-semibold text-muted-foreground uppercase tracking-wider px-3\",\n                                children: \"Kategori Cepat\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 gap-2\",\n                                children: data.quickActions.map((action)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: action.url,\n                                        className: \"p-3 rounded-xl bg-gradient-to-br \".concat(action.color, \" text-white hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg group flex flex-col items-center\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(action.icon, {\n                                                className: \"w-5 h-5 mb-1\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs font-medium\",\n                                                children: action.name\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, action.name, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 143,\n                                        columnNumber: 15\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 137,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarFooter, {\n                className: \"p-4 border-t-0\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_nav_user__WEBPACK_IMPORTED_MODULE_5__.NavUser, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 85,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"DGGHQYN3nvUdJRHCgqdfrb97I+0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_themes__WEBPACK_IMPORTED_MODULE_3__.useTheme\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime1(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\")), \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\");\n_c2 = MessageItem;\nconst ChatInput = /*#__PURE__*/ _s1((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c3 = _s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage1, setNewMessage1] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            if (!newMessage1.trim() || disabled) return;\n            onSendMessage(newMessage1.trim());\n            setNewMessage1('');\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        newMessage1,\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress1 = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                value: newMessage1,\n                                onChange: (e)=>setNewMessage1(e.target.value),\n                                onKeyPress: handleKeyPress1,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 169,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage1.trim() || disabled,\n                        className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 191,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 197,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[#2DD4BF] animate-pulse\",\n                        children: \"AI is responding...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 196,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, undefined);\n}, \"NxOLm5QyPnm/PyCbYiTLRL9m/F4=\")), \"NxOLm5QyPnm/PyCbYiTLRL9m/F4=\");\n_c4 = ChatInput;\nfunction ChatInterface(param) {\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt1, _chat_character_name1, _chat_character6, _chat_character7;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    setStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 401,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 402,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 386,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 419,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 418,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 417,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 431,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 432,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 416,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 415,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message, index)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                            children: [\n                                message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"w-8 h-8 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                            alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                            children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                            content: message.content,\n                                            role: message.role\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 468,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: formatTime(message.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 469,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 447,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-2 justify-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"w-8 h-8 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name1 = _chat_character6.name) === null || _chat_character_name1 === void 0 ? void 0 : (_chat_character_name_charAt1 = _chat_character_name1.charAt(0)) === null || _chat_character_name_charAt1 === void 0 ? void 0 : _chat_character_name_charAt1.toUpperCase()) || 'C'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 491,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 489,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                                children: [\n                                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                        content: streamingMessage,\n                                        role: \"assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Thinking\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 503,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 513,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 445,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        value: newMessage,\n                                        onChange: (e)=>setNewMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Message \".concat(((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'character', \"...\"),\n                                        disabled: sending || isStreaming,\n                                        className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 527,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                        children: (sending || isStreaming) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 526,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !newMessage.trim() || sending || isStreaming,\n                                className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 543,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 525,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 554,\n                                columnNumber: 11\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#2DD4BF] animate-pulse\",\n                                children: \"AI is responding...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 556,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 553,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 412,\n        columnNumber: 5\n    }, this);\n}\n_s2(ChatInterface, \"3uqWb+KfEPAe9PSS4dRcajUXxhg=\");\n_c5 = ChatInterface;\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem$memo\");\n$RefreshReg$(_c2, \"MessageItem\");\n$RefreshReg$(_c3, \"ChatInput$memo\");\n$RefreshReg$(_c4, \"ChatInput\");\n$RefreshReg$(_c5, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
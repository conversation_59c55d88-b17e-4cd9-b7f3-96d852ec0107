"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/page.tsx":
/*!************************************!*\
  !*** ./src/app/dashboard/page.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Page)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_character_card__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/character-card */ \"(app-pages-browser)/./src/components/character-card.tsx\");\n/* harmony import */ var _components_character_search__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/character-search */ \"(app-pages-browser)/./src/components/character-search.tsx\");\n/* harmony import */ var _components_pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/pagination */ \"(app-pages-browser)/./src/components/pagination.tsx\");\n/* harmony import */ var _services_character__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/services/character */ \"(app-pages-browser)/./src/services/character.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction Page() {\n    _s();\n    const { isAuthenticated, user, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [characters, setCharacters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [charactersLoading, setCharactersLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [searchParams, setSearchParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 12\n    });\n    const [availableTags, setAvailableTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // Fetch characters\n    const fetchCharacters = async (params)=>{\n        try {\n            setCharactersLoading(true);\n            const response = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.getCharacters(params);\n            setCharacters(response.data);\n            setTotalPages(response.totalPages);\n            setCurrentPage(response.currentPage);\n        } catch (error) {\n            console.error('Failed to fetch characters:', error);\n        } finally{\n            setCharactersLoading(false);\n        }\n    };\n    // Load available tags on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            const loadTags = {\n                \"Page.useEffect.loadTags\": async ()=>{\n                    const tags = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.getAllTags();\n                    setAvailableTags(tags);\n                }\n            }[\"Page.useEffect.loadTags\"];\n            loadTags();\n        }\n    }[\"Page.useEffect\"], []);\n    // Load characters on mount and when search params change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Page.useEffect\": ()=>{\n            fetchCharacters(searchParams);\n        }\n    }[\"Page.useEffect\"], [\n        searchParams\n    ]);\n    // Handle search\n    const handleSearch = (params)=>{\n        const newParams = {\n            ...searchParams,\n            ...params\n        };\n        setSearchParams(newParams);\n    };\n    // Handle page change\n    const handlePageChange = (page)=>{\n        const newParams = {\n            ...searchParams,\n            page\n        };\n        setSearchParams(newParams);\n    };\n    // Handle start chat\n    const handleStartChat = async (characterId)=>{\n        if (!isAuthenticated) {\n            alert('Please sign in to start chatting');\n            return;\n        }\n        try {\n            const chatSession = await _services_character__WEBPACK_IMPORTED_MODULE_11__.characterService.initiateChat(characterId);\n            console.log('Chat initiated:', chatSession);\n            // Navigate to chat page with the new chat\n            router.push(\"/chat?id=\".concat(chatSession.id));\n        } catch (error) {\n            console.error('Failed to initiate chat:', error);\n            alert('Failed to start chat. Please try again.');\n        }\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-screen\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-lg\",\n                            children: \"Loading...\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 100,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n            lineNumber: 98,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarInset, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_6__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_5__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 127,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                                    children: \"Character Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 flex-col gap-6 p-4 pt-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-2xl font-bold mb-2\",\n                                        children: isAuthenticated ? \"Welcome back, \".concat(user === null || user === void 0 ? void 0 : user.name, \"!\") : 'Welcome to Bestieku'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground\",\n                                        children: isAuthenticated ? 'Choose a character to start chatting with AI companions in immersive stories.' : 'Explore our AI characters and sign in to start chatting in immersive stories.'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_search__WEBPACK_IMPORTED_MODULE_9__.CharacterSearch, {\n                                onSearch: handleSearch,\n                                isLoading: charactersLoading,\n                                availableTags: availableTags\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, this),\n                            charactersLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: Array.from({\n                                    length: 8\n                                }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-card border rounded-xl p-4 animate-pulse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-muted/50 aspect-square rounded-lg mb-3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-4 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-3 bg-muted/50 rounded w-2/3\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, i, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this) : characters.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4\",\n                                children: characters.map((character)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_character_card__WEBPACK_IMPORTED_MODULE_8__.CharacterCard, {\n                                        character: character,\n                                        onStartChat: handleStartChat\n                                    }, character.id, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 171,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center py-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"No characters found matching your criteria.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 180,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: \"Try adjusting your search or filters.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 13\n                            }, this),\n                            !charactersLoading && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_pagination__WEBPACK_IMPORTED_MODULE_10__.Pagination, {\n                                currentPage: currentPage,\n                                totalPages: totalPages,\n                                onPageChange: handlePageChange,\n                                isLoading: charactersLoading\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 13\n                            }, this),\n                            !isAuthenticated && characters.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-8 p-6 bg-muted/30 rounded-xl text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: \"Ready to start chatting?\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-muted-foreground mb-4\",\n                                        children: \"Sign in to unlock personalized conversations and save your chat history.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 112,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 110,\n        columnNumber: 5\n    }, this);\n}\n_s(Page, \"/tHOeQgZqnMNjZAGRYx1liIFUhA=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_7__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = Page;\nvar _c;\n$RefreshReg$(_c, \"Page\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/page.tsx\n"));

/***/ })

});
'use client';

import React from 'react';
import { Character } from '@/types/character';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { MessageCircle, Star } from 'lucide-react';
import { useAuth } from '@/contexts/auth-context';

interface CharacterCardProps {
  character: Character;
  onStartChat?: (characterId: string) => void;
}

export function CharacterCard({ character, onStartChat }: CharacterCardProps) {
  const { isAuthenticated } = useAuth();

  const handleStartChat = () => {
    if (onStartChat) {
      onStartChat(character.id);
    }
  };

  return (
    <div className="bg-card border rounded-xl p-4 hover:shadow-md transition-shadow group">
      {/* Character Image */}
      <div className="relative mb-3">
        <div className="bg-muted/50 aspect-square rounded-lg overflow-hidden">
          {character.image ? (
            <img
              src={character.image}
              alt={character.name}
              className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-muted-foreground">
              <MessageCircle className="w-12 h-12" />
            </div>
          )}
        </div>
        
        {/* Story Mode Badge */}
        {character.storyMode && (
          <Badge
            variant="secondary"
            className="absolute top-2 right-2 bg-bestieku-primary text-white"
          >
            Story Mode
          </Badge>
        )}
      </div>

      {/* Character Info */}
      <div className="space-y-2">
        <h3 className="font-semibold text-lg leading-tight">{character.name}</h3>
        
        <p className="text-sm text-muted-foreground line-clamp-2 min-h-[2.5rem]">
          {character.description}
        </p>

        {/* Tags */}
        {character.tags && character.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {character.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant="outline" className="text-xs">
                {tag}
              </Badge>
            ))}
            {character.tags.length > 3 && (
              <Badge variant="outline" className="text-xs">
                +{character.tags.length - 3}
              </Badge>
            )}
          </div>
        )}

        {/* Action Button */}
        <div className="pt-2">
          {isAuthenticated ? (
            <Button
              onClick={handleStartChat}
              className="w-full bg-bestieku-primary hover:bg-bestieku-primary-dark"
              size="sm"
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Start Chat
            </Button>
          ) : (
            <Button
              variant="outline"
              className="w-full"
              size="sm"
              disabled
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Sign in to Chat
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c1 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { streamingMessage, characterImage, characterName } = param;\n    var _characterName_charAt;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Thinking\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Typing...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = StreamingMessage;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[#2DD4BF] animate-pulse\",\n                        children: \"AI is responding...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, undefined);\n}, \"1o5B5G/jtGrG8z8Ow45dQHalZdE=\"));\n_c3 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s2((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c4 = _s2((param)=>{\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt, _chat_character_name, _chat_character6, _chat_character7, _chat_character8, _chat_character9, _chat_character10;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Stable references for character data to prevent unnecessary re-renders\n    const characterImageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image);\n    const characterNameRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)((_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name);\n    // Update refs when chat changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            var _chat_character, _chat_character1;\n            characterImageRef.current = (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image;\n            characterNameRef.current = (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        (_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : _chat_character2.image,\n        (_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 509,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 508,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 507,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 521,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 520,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 518,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 516,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 506,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                                    alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name = _chat_character6.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 545,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 551,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 552,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 549,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 534,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                            message: message,\n                            characterImage: characterImageRef.current,\n                            characterName: characterNameRef.current\n                        }, message.id, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, undefined)),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                        streamingMessage: streamingMessage,\n                        characterImage: (_chat_character8 = chat.character) === null || _chat_character8 === void 0 ? void 0 : _chat_character8.image,\n                        characterName: (_chat_character9 = chat.character) === null || _chat_character9 === void 0 ? void 0 : _chat_character9.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 577,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 565,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: (_chat_character10 = chat.character) === null || _chat_character10 === void 0 ? void 0 : _chat_character10.name,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 588,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 532,\n        columnNumber: 5\n    }, undefined);\n}, \"bjqg6lHcqbbXbK8sidsGw7LiDUU=\")), \"bjqg6lHcqbbXbK8sidsGw7LiDUU=\");\n_c5 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem\");\n$RefreshReg$(_c2, \"StreamingMessage\");\n$RefreshReg$(_c3, \"ChatInput\");\n$RefreshReg$(_c4, \"ChatInterface$memo\");\n$RefreshReg$(_c5, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
'use client';

import { useState, useEffect, Suspense } from 'react';
import { useSearchPara<PERSON>, useRouter } from 'next/navigation';
import { Chat } from '@/types/chat';
import { ChatList } from '@/components/chat/chat-list';
import { ChatInterface } from '@/components/chat/chat-interface';
import { CharacterProfileSidebar } from '@/components/chat/character-profile-sidebar';
import { useAuth } from '@/contexts/auth-context';
import { AuthModal } from '@/components/auth/auth-modal';
import { AppSidebar } from "@/components/app-sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar";
import { MessageCircle, Menu } from 'lucide-react';

function ChatPageContent() {
  const { isAuthenticated, isLoading } = useAuth();
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [isProfileOpen, setIsProfileOpen] = useState(true);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [isChatListOpen, setIsChatListOpen] = useState(true);
  const [isMobile, setIsMobile] = useState(false);
  const [isMobileProfileOpen, setIsMobileProfileOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const chatId = searchParams.get('id');

  // Detect mobile screen size
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Mobile behavior: close chat list when chat is selected, close profile by default
  useEffect(() => {
    if (isMobile) {
      if (selectedChat) {
        setIsChatListOpen(false);
      }
      setIsProfileOpen(false);
    } else {
      setIsChatListOpen(true);
      setIsProfileOpen(true);
    }
  }, [isMobile, selectedChat]);

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      // Show auth modal instead of redirecting
      setShowAuthModal(true);
    }
  }, [isAuthenticated, isLoading]);

  // Handle chats loaded callback
  const handleChatsLoaded = (chats: Chat[]) => {
    console.log('Chats loaded:', chats);

    // Auto-select chat from URL if available
    if (chatId && !selectedChat) {
      const targetChat = chats.find(chat => chat.id === chatId);
      if (targetChat) {
        console.log('Auto-selecting chat from URL:', targetChat);
        setSelectedChat(targetChat);
      } else {
        console.log('Chat not found in loaded chats:', chatId);
        // Remove invalid chat ID from URL
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('id');
        window.history.replaceState({}, '', newUrl.toString());
      }
    }
  };

  const handleChatSelect = (chat: Chat) => {
    setSelectedChat(chat);
    // Update URL with chat ID
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set('id', chat.id);
    window.history.pushState({}, '', newUrl.toString());

    // On mobile, close chat list when chat is selected
    if (isMobile) {
      setIsChatListOpen(false);
    }
  };

  const handleBackToList = () => {
    setSelectedChat(null);
    // Remove chat ID from URL
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.delete('id');
    window.history.pushState({}, '', newUrl.toString());

    // On mobile, show chat list when going back
    if (isMobile) {
      setIsChatListOpen(true);
    }
  };

  // Toggle chat list (for mobile)
  const toggleChatList = () => {
    setIsChatListOpen(!isChatListOpen);
  };

  // Toggle mobile profile
  const toggleMobileProfile = () => {
    setIsMobileProfileOpen(!isMobileProfileOpen);
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="h-screen flex items-center justify-center">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
              <p className="text-muted-foreground">Loading...</p>
            </div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  if (!isAuthenticated) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset className="flex flex-col h-screen overflow-hidden">
          <header className="flex h-16 shrink-0 items-center gap-2 border-b">
            <div className="flex items-center gap-2 px-4">
              <SidebarTrigger className="-ml-1" />
              <Separator
                orientation="vertical"
                className="mr-2 data-[orientation=vertical]:h-4"
              />
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem className="hidden md:block">
                    <BreadcrumbLink href="/dashboard">
                      Bestieku
                    </BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator className="hidden md:block" />
                  <BreadcrumbItem>
                    <BreadcrumbPage>Chat</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
            </div>
          </header>

          {/* Main Chat Area - Unauthenticated */}
          <div className="flex flex-1 min-h-0">
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center max-w-md mx-auto p-8">
                <MessageCircle className="w-16 h-16 mx-auto mb-6 text-muted-foreground/50" />
                <h2 className="text-xl font-semibold mb-2">Selamat Datang di Bestieku Chat</h2>
                <p className="text-muted-foreground mb-6">
                  Untuk mulai mengobrol dengan karakter AI favorit Anda, silakan masuk atau daftar terlebih dahulu.
                </p>
                <button
                  onClick={() => setShowAuthModal(true)}
                  className="inline-flex items-center px-6 py-3 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors font-medium"
                >
                  Masuk / Daftar
                </button>
              </div>
            </div>
          </div>
        </SidebarInset>

        {/* Auth Modal */}
        <AuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
        />
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex flex-col h-screen overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4 w-full">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb className="flex-1">
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Chat</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>

            {/* Mobile chat list toggle button */}
            {isMobile && selectedChat && (
              <button
                onClick={toggleChatList}
                className="p-2 hover:bg-muted rounded-lg transition-colors md:hidden"
                aria-label="Toggle chat list"
              >
                <Menu className="h-5 w-5" />
              </button>
            )}
          </div>
        </header>

        {/* Main Chat Area - Responsive layout */}
        <div className="flex flex-1 min-h-0 relative">
          {/* Chat List Sidebar - Responsive width and positioning */}
          <div className={`
            ${isMobile
              ? `absolute inset-y-0 left-0 z-20 w-full bg-background transform transition-transform duration-300 ${
                  isChatListOpen ? 'translate-x-0' : '-translate-x-full'
                }`
              : 'w-80 border-r bg-background'
            }
            flex flex-col min-h-0
          `}>
            <ChatList
              selectedChatId={selectedChat?.id}
              onChatSelect={handleChatSelect}
              onChatsLoaded={handleChatsLoaded}
            />
          </div>

          {/* Chat Interface - Responsive layout */}
          <div className={`
            ${isMobile ? 'flex-1' : 'flex-1'}
            flex min-h-0
          `}>
            <div className="flex-1 flex flex-col min-h-0">
              {selectedChat ? (
                <ChatInterface
                  chat={selectedChat}
                  onBack={handleBackToList}
                  onToggleProfile={isMobile ? toggleMobileProfile : undefined}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center">
                  <div className="text-center max-w-md mx-auto p-8">
                    <MessageCircle className="w-16 h-16 mx-auto mb-6 text-muted-foreground/50" />
                    <h2 className="text-xl font-semibold mb-2">Welcome to Bestieku Chat</h2>
                    <p className="text-muted-foreground mb-6">
                      {isMobile
                        ? "Tap the menu to select a chat or browse characters to start a new conversation."
                        : "Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation."
                      }
                    </p>
                    <div className="space-y-3">
                      {isMobile && (
                        <button
                          onClick={toggleChatList}
                          className="inline-flex items-center px-4 py-2 bg-muted hover:bg-muted/80 text-foreground rounded-lg transition-colors w-full justify-center"
                        >
                          <MessageCircle className="mr-2 h-4 w-4" />
                          View Chats
                        </button>
                      )}
                      <button
                        onClick={() => router.push('/dashboard')}
                        className="inline-flex items-center px-4 py-2 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors w-full justify-center"
                      >
                        Browse Characters
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Character Profile Sidebar - Hidden on mobile by default */}
            {selectedChat && !isMobile && (
              <div className="flex-shrink-0 min-h-0">
                <CharacterProfileSidebar
                  characterId={selectedChat.characterId}
                  messageCount={selectedChat.messageCount}
                  isOpen={isProfileOpen}
                  onToggle={() => setIsProfileOpen(!isProfileOpen)}
                />
              </div>
            )}
          </div>

          {/* Mobile overlay when chat list is open */}
          {isMobile && isChatListOpen && (
            <div
              className="absolute inset-0 bg-black/50 z-10"
              onClick={() => setIsChatListOpen(false)}
            />
          )}

          {/* Mobile character profile overlay */}
          {isMobile && selectedChat && isMobileProfileOpen && (
            <>
              <div
                className="absolute inset-0 bg-black/50 z-30"
                onClick={() => setIsMobileProfileOpen(false)}
              />
              <div className="absolute inset-y-0 right-0 z-40 w-80 max-w-[90vw] bg-background transform transition-transform duration-300">
                <CharacterProfileSidebar
                  characterId={selectedChat.characterId}
                  messageCount={selectedChat.messageCount}
                  isOpen={true}
                  onToggle={() => setIsMobileProfileOpen(false)}
                />
              </div>
            </>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}

export default function ChatPage() {
  return (
    <Suspense fallback={
      <div className="h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading chat...</p>
        </div>
      </div>
    }>
      <ChatPageContent />
    </Suspense>
  );
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction ChatInterface(param) {\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt1, _chat_character_name1, _chat_character6, _chat_character7;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim() || sending) return;\n        const messageText = newMessage.trim();\n        const tempId = \"temp-\".concat(Date.now());\n        setNewMessage('');\n        setSending(true);\n        try {\n            // Add user message to UI immediately\n            const userMessage = {\n                id: tempId,\n                role: 'user',\n                content: messageText,\n                contentType: 'text',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            console.log('Sending message to chat:', chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            // Send message with streaming enabled\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            console.log('Message sent successfully:', response);\n            // Update the temporary message with real ID if available\n            if (response.id) {\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempId ? {\n                            ...msg,\n                            id: response.id\n                        } : msg));\n            }\n            // Start streaming response\n            await startStreaming();\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            // Remove the temporary user message on error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== tempId));\n            alert('Failed to send message. Please try again.');\n        } finally{\n            setSending(false);\n        }\n    };\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    setStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    const formatTime = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_6__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            return '';\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 232,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 233,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 229,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 217,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 249,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 256,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 248,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 262,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 269,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 245,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message, index)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                            children: [\n                                message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"w-8 h-8 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                            alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 285,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                            children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 286,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 284,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                            children: message.content\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 299,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: formatTime(message.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 303,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 308,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 307,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 278,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-2 justify-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"w-8 h-8 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name1 = _chat_character6.name) === null || _chat_character_name1 === void 0 ? void 0 : (_chat_character_name_charAt1 = _chat_character_name1.charAt(0)) === null || _chat_character_name_charAt1 === void 0 ? void 0 : _chat_character_name_charAt1.toUpperCase()) || 'C'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                                children: [\n                                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n                                        children: streamingMessage\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 329,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Thinking\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 341,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        value: newMessage,\n                                        onChange: (e)=>setNewMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Message \".concat(((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'character', \"...\"),\n                                        disabled: sending || isStreaming,\n                                        className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                        children: (sending || isStreaming) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !newMessage.trim() || sending || isStreaming,\n                                className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 374,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 11\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#2DD4BF] animate-pulse\",\n                                children: \"AI is responding...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 384,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 243,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"HWCezY2ns3dsLvFKUIeDCj1R1D4=\");\n_c = ChatInterface;\nvar _c;\n$RefreshReg$(_c, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
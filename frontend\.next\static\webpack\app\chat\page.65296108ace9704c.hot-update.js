"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar),\n/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),\n/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),\n/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),\n/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),\n/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),\n/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),\n/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),\n/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),\n/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),\n/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),\n/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),\n/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),\n/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),\n/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),\n/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),\n/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),\n/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),\n/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),\n/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),\n/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),\n/* harmony export */   useSidebar: () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(app-pages-browser)/./src/hooks/use-mobile.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar,SidebarContent,SidebarFooter,SidebarGroup,SidebarGroupAction,SidebarGroupContent,SidebarGroupLabel,SidebarHeader,SidebarInput,SidebarInset,SidebarMenu,SidebarMenuAction,SidebarMenuBadge,SidebarMenuButton,SidebarMenuItem,SidebarMenuSkeleton,SidebarMenuSub,SidebarMenuSubButton,SidebarMenuSubItem,SidebarProvider,SidebarRail,SidebarSeparator,SidebarTrigger,useSidebar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = \"16rem\";\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\nconst SidebarContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\nfunction useSidebar() {\n    _s();\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SidebarContext);\n    if (!context) {\n        throw new Error(\"useSidebar must be used within a SidebarProvider.\");\n    }\n    return context;\n}\n_s(useSidebar, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction SidebarProvider(param) {\n    let { defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props } = param;\n    _s1();\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const [openMobile, setOpenMobile] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultOpen);\n    const open = openProp !== null && openProp !== void 0 ? openProp : _open;\n    const setOpen = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[setOpen]\": (value)=>{\n            const openState = typeof value === \"function\" ? value(open) : value;\n            if (setOpenProp) {\n                setOpenProp(openState);\n            } else {\n                _setOpen(openState);\n            }\n            // This sets the cookie to keep the sidebar state.\n            document.cookie = \"\".concat(SIDEBAR_COOKIE_NAME, \"=\").concat(openState, \"; path=/; max-age=\").concat(SIDEBAR_COOKIE_MAX_AGE);\n        }\n    }[\"SidebarProvider.useCallback[setOpen]\"], [\n        setOpenProp,\n        open\n    ]);\n    // Helper to toggle the sidebar.\n    const toggleSidebar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[toggleSidebar]\": ()=>{\n            return isMobile ? setOpenMobile({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]) : setOpen({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]);\n        }\n    }[\"SidebarProvider.useCallback[toggleSidebar]\"], [\n        isMobile,\n        setOpen,\n        setOpenMobile\n    ]);\n    // Adds a keyboard shortcut to toggle the sidebar.\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SidebarProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"SidebarProvider.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n                        event.preventDefault();\n                        toggleSidebar();\n                    }\n                }\n            }[\"SidebarProvider.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"SidebarProvider.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"SidebarProvider.useEffect\"];\n        }\n    }[\"SidebarProvider.useEffect\"], [\n        toggleSidebar\n    ]);\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\";\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarProvider.useMemo[contextValue]\": ()=>({\n                state,\n                open,\n                setOpen,\n                isMobile,\n                openMobile,\n                setOpenMobile,\n                toggleSidebar\n            })\n    }[\"SidebarProvider.useMemo[contextValue]\"], [\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n            delayDuration: 0,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-wrapper\",\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH,\n                    \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                    ...style\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\", className),\n                ...props,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s1(SidebarProvider, \"QSOkjq1AvKFJW5+zwiK52jPX7zI=\", false, function() {\n    return [\n        _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c = SidebarProvider;\nfunction Sidebar(param) {\n    let { side = \"left\", variant = \"sidebar\", collapsible = \"offcanvas\", className, children, ...props } = param;\n    _s2();\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n    if (collapsible === \"none\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-slot\": \"sidebar\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\", className),\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.Sheet, {\n            open: openMobile,\n            onOpenChange: setOpenMobile,\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetContent, {\n                \"data-sidebar\": \"sidebar\",\n                \"data-slot\": \"sidebar\",\n                \"data-mobile\": \"true\",\n                className: \"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\",\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE\n                },\n                side: side,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetHeader, {\n                        className: \"sr-only\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTitle, {\n                                children: \"Sidebar\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetDescription, {\n                                children: \"Displays the mobile sidebar.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full flex-col\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group peer text-sidebar-foreground hidden md:block\",\n        \"data-state\": state,\n        \"data-collapsible\": state === \"collapsed\" ? collapsible : \"\",\n        \"data-variant\": variant,\n        \"data-side\": side,\n        \"data-slot\": \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-gap\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\", \"group-data-[collapsible=offcanvas]:w-0\", \"group-data-[side=right]:rotate-180\", variant === \"floating\" || variant === \"inset\" ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\" : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\")\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-container\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\", side === \"left\" ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\" : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\", // Adjust the padding for floating and inset variants.\n                variant === \"floating\" || variant === \"inset\" ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\" : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\", className),\n                ...props,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-sidebar\": \"sidebar\",\n                    \"data-slot\": \"sidebar-inner\",\n                    className: \"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s2(Sidebar, \"hAL3+uRFwO9tnbDK50BUE5wZ71s=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c1 = Sidebar;\nfunction SidebarTrigger(param) {\n    let { className, onClick, ...props } = param;\n    _s3();\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n        \"data-sidebar\": \"trigger\",\n        \"data-slot\": \"sidebar-trigger\",\n        variant: \"ghost\",\n        size: \"icon\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-7\", className),\n        onClick: (event)=>{\n            onClick === null || onClick === void 0 ? void 0 : onClick(event);\n            toggleSidebar();\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LayoutSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle Sidebar\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s3(SidebarTrigger, \"dRnjPhQbCChcVGr4xvQkpNxnqyg=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c2 = SidebarTrigger;\nfunction SidebarRail(param) {\n    let { className, ...props } = param;\n    _s4();\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        \"data-sidebar\": \"rail\",\n        \"data-slot\": \"sidebar-rail\",\n        \"aria-label\": \"Toggle Sidebar\",\n        tabIndex: -1,\n        onClick: toggleSidebar,\n        title: \"Toggle Sidebar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\", \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\", \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\", \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\", \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\", \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s4(SidebarRail, \"dRnjPhQbCChcVGr4xvQkpNxnqyg=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c3 = SidebarRail;\nfunction SidebarInset(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        \"data-slot\": \"sidebar-inset\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-background relative flex w-full flex-1 flex-col\", \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_c4 = SidebarInset;\nfunction SidebarInput(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n        \"data-slot\": \"sidebar-input\",\n        \"data-sidebar\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-background h-8 w-full shadow-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, this);\n}\n_c5 = SidebarInput;\nfunction SidebarHeader(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-header\",\n        \"data-sidebar\": \"header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, this);\n}\n_c6 = SidebarHeader;\nfunction SidebarFooter(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-footer\",\n        \"data-sidebar\": \"footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_c7 = SidebarFooter;\nfunction SidebarSeparator(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n        \"data-slot\": \"sidebar-separator\",\n        \"data-sidebar\": \"separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-sidebar-border mx-2 w-auto\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_c8 = SidebarSeparator;\nfunction SidebarContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-content\",\n        \"data-sidebar\": \"content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, this);\n}\n_c9 = SidebarContent;\nfunction SidebarGroup(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-group\",\n        \"data-sidebar\": \"group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex w-full min-w-0 flex-col p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, this);\n}\n_c10 = SidebarGroup;\nfunction SidebarGroupLabel(param) {\n    let { className, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : \"div\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-group-label\",\n        \"data-sidebar\": \"group-label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_c11 = SidebarGroupLabel;\nfunction SidebarGroupAction(param) {\n    let { className, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-group-action\",\n        \"data-sidebar\": \"group-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, this);\n}\n_c12 = SidebarGroupAction;\nfunction SidebarGroupContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-group-content\",\n        \"data-sidebar\": \"group-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 445,\n        columnNumber: 5\n    }, this);\n}\n_c13 = SidebarGroupContent;\nfunction SidebarMenu(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        \"data-slot\": \"sidebar-menu\",\n        \"data-sidebar\": \"menu\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex w-full min-w-0 flex-col gap-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, this);\n}\n_c14 = SidebarMenu;\nfunction SidebarMenuItem(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"sidebar-menu-item\",\n        \"data-sidebar\": \"menu-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/menu-item relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 467,\n        columnNumber: 5\n    }, this);\n}\n_c15 = SidebarMenuItem;\nconst sidebarMenuButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n            outline: \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\"\n        },\n        size: {\n            default: \"h-8 text-sm\",\n            sm: \"h-7 text-xs\",\n            lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction SidebarMenuButton(param) {\n    let { asChild = false, isActive = false, variant = \"default\", size = \"default\", tooltip, className, ...props } = param;\n    _s5();\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : \"button\";\n    const { isMobile, state } = useSidebar();\n    const button = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-button\",\n        \"data-sidebar\": \"menu-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(sidebarMenuButtonVariants({\n            variant,\n            size\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n    if (!tooltip) {\n        return button;\n    }\n    if (typeof tooltip === \"string\") {\n        tooltip = {\n            children: tooltip\n        };\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                asChild: true,\n                children: button\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                side: \"right\",\n                align: \"center\",\n                hidden: state !== \"collapsed\" || isMobile,\n                ...tooltip\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 536,\n        columnNumber: 5\n    }, this);\n}\n_s5(SidebarMenuButton, \"DSCdbs8JtpmKVxCYgM7sPAZNgB0=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c16 = SidebarMenuButton;\nfunction SidebarMenuAction(param) {\n    let { className, asChild = false, showOnHover = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-action\",\n        \"data-sidebar\": \"menu-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", showOnHover && \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 560,\n        columnNumber: 5\n    }, this);\n}\n_c17 = SidebarMenuAction;\nfunction SidebarMenuBadge(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-menu-badge\",\n        \"data-sidebar\": \"menu-badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\", \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_c18 = SidebarMenuBadge;\nfunction SidebarMenuSkeleton(param) {\n    let { className, showIcon = false, ...props } = param;\n    _s6();\n    // Random width between 50 to 90%.\n    const width = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarMenuSkeleton.useMemo[width]\": ()=>{\n            return \"\".concat(Math.floor(Math.random() * 40) + 50, \"%\");\n        }\n    }[\"SidebarMenuSkeleton.useMemo[width]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-menu-skeleton\",\n        \"data-sidebar\": \"menu-skeleton\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-8 items-center gap-2 rounded-md px-2\", className),\n        ...props,\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"size-4 rounded-md\",\n                \"data-sidebar\": \"menu-skeleton-icon\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 622,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"h-4 max-w-(--skeleton-width) flex-1\",\n                \"data-sidebar\": \"menu-skeleton-text\",\n                style: {\n                    \"--skeleton-width\": width\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, this);\n}\n_s6(SidebarMenuSkeleton, \"nKFjX4dxbYo91VAj5VdWQ1XUe3I=\");\n_c19 = SidebarMenuSkeleton;\nfunction SidebarMenuSub(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        \"data-slot\": \"sidebar-menu-sub\",\n        \"data-sidebar\": \"menu-sub\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 642,\n        columnNumber: 5\n    }, this);\n}\n_c20 = SidebarMenuSub;\nfunction SidebarMenuSubItem(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"sidebar-menu-sub-item\",\n        \"data-sidebar\": \"menu-sub-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/menu-sub-item relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 660,\n        columnNumber: 5\n    }, this);\n}\n_c21 = SidebarMenuSubItem;\nfunction SidebarMenuSubButton(param) {\n    let { asChild = false, size = \"md\", isActive = false, className, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.Slot : \"a\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-sub-button\",\n        \"data-sidebar\": \"menu-sub-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\", \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\", size === \"sm\" && \"text-xs\", size === \"md\" && \"text-sm\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 683,\n        columnNumber: 5\n    }, this);\n}\n_c22 = SidebarMenuSubButton;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;\n$RefreshReg$(_c, \"SidebarProvider\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"SidebarTrigger\");\n$RefreshReg$(_c3, \"SidebarRail\");\n$RefreshReg$(_c4, \"SidebarInset\");\n$RefreshReg$(_c5, \"SidebarInput\");\n$RefreshReg$(_c6, \"SidebarHeader\");\n$RefreshReg$(_c7, \"SidebarFooter\");\n$RefreshReg$(_c8, \"SidebarSeparator\");\n$RefreshReg$(_c9, \"SidebarContent\");\n$RefreshReg$(_c10, \"SidebarGroup\");\n$RefreshReg$(_c11, \"SidebarGroupLabel\");\n$RefreshReg$(_c12, \"SidebarGroupAction\");\n$RefreshReg$(_c13, \"SidebarGroupContent\");\n$RefreshReg$(_c14, \"SidebarMenu\");\n$RefreshReg$(_c15, \"SidebarMenuItem\");\n$RefreshReg$(_c16, \"SidebarMenuButton\");\n$RefreshReg$(_c17, \"SidebarMenuAction\");\n$RefreshReg$(_c18, \"SidebarMenuBadge\");\n$RefreshReg$(_c19, \"SidebarMenuSkeleton\");\n$RefreshReg$(_c20, \"SidebarMenuSub\");\n$RefreshReg$(_c21, \"SidebarMenuSubItem\");\n$RefreshReg$(_c22, \"SidebarMenuSubButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sidebar.tsx\n"));

/***/ })

});
'use client';

import React, { useState, useEffect } from 'react';
import { Chat } from '@/types/chat';
import { chatService } from '@/services/chat';
import { characterService } from '@/services/character';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Search, MessageCircle } from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';

interface ChatListProps {
  selectedChatId?: string;
  onChatSelect: (chat: Chat) => void;
  onChatsLoaded?: (chats: Chat[]) => void;
}

export function ChatList({ selectedChatId, onChatSelect, onChatsLoaded }: ChatListProps) {
  const [chats, setChats] = useState<Chat[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    loadChats();
  }, []);

  const loadChats = async () => {
    try {
      setLoading(true);
      const response = await chatService.getChats({ limit: 50 });
      
      // Enrich chats with character data
      const enrichedChats = await Promise.all(
        response.data.map(async (chat) => {
          try {
            const character = await characterService.getCharacterById(chat.characterId);
            return {
              ...chat,
              character: {
                id: character.id,
                name: character.name,
                image: character.image,
              },
            };
          } catch (error) {
            console.error(`Failed to load character ${chat.characterId}:`, error);
            return {
              ...chat,
              character: {
                id: chat.characterId,
                name: 'Unknown Character',
                image: '',
              },
            };
          }
        })
      );

      setChats(enrichedChats);

      // Notify parent that chats are loaded
      if (onChatsLoaded) {
        onChatsLoaded(enrichedChats);
      }
    } catch (error) {
      console.error('Failed to load chats:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredChats = chats.filter(chat =>
    chat.character?.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    chat.latestMessage?.content.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatMessagePreview = (content: string, maxLength: number = 50) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  const formatTime = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return '';
    }
  };

  if (loading) {
    return (
      <div className="w-80 border-r bg-background">
        <div className="p-4 border-b">
          <div className="h-10 bg-muted animate-pulse rounded" />
        </div>
        <div className="space-y-2 p-2">
          {Array.from({ length: 5 }).map((_, i) => (
            <div key={i} className="p-3 rounded-lg animate-pulse">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-muted rounded-full" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      {/* Header - Fixed */}
      <div className="flex-shrink-0 p-4 border-b bg-background/50">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">Chats</h2>
          <div className="flex items-center gap-1">
            <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            <span className="text-xs text-muted-foreground">{filteredChats.length}</span>
          </div>
        </div>
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
          <Input
            placeholder="Search chats..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 rounded-xl border-2 focus:border-[#2DD4BF] transition-colors"
          />
        </div>
      </div>

      {/* Chat List - Scrollable */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden">
        <div className="h-full">
          {filteredChats.length === 0 ? (
            <div className="p-8 text-center text-muted-foreground">
              <MessageCircle className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p className="text-sm">
                {searchTerm ? 'No chats found' : 'No chats yet'}
              </p>
              <p className="text-xs mt-1">
                {searchTerm ? 'Try a different search term' : 'Start a conversation with a character'}
              </p>
            </div>
          ) : (
            <div className="space-y-1 p-2">
            {filteredChats.map((chat) => (
              <div
                key={chat.id}
                onClick={() => onChatSelect(chat)}
                className={`p-3 rounded-xl cursor-pointer transition-all duration-200 hover:bg-muted/50 hover:scale-[1.02] ${
                  selectedChatId === chat.id
                    ? 'bg-bestieku-primary/10 border-2 border-bestieku-primary/30 shadow-md'
                    : 'border-2 border-transparent hover:border-muted'
                }`}
              >
                <div className="flex items-center space-x-3">
                  {/* Character Avatar */}
                  <div className="relative">
                    <Avatar className="w-12 h-12 ring-2 ring-bestieku-primary/20">
                      <AvatarImage src={chat.character?.image} alt={chat.character?.name} />
                      <AvatarFallback className="bg-bestieku-primary/10 text-bestieku-primary font-semibold">
                        {chat.character?.name?.charAt(0)?.toUpperCase() || 'C'}
                      </AvatarFallback>
                    </Avatar>
                    {/* Online indicator */}
                    <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full"></div>
                  </div>

                  {/* Chat Info */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-1">
                      <h3 className="font-medium text-sm truncate">
                        {chat.character?.name || 'Unknown Character'}
                      </h3>
                      {chat.latestMessage && (
                        <span className="text-xs text-muted-foreground">
                          {formatTime(chat.latestMessage.createdAt)}
                        </span>
                      )}
                    </div>
                    
                    {chat.latestMessage ? (
                      <p className="text-sm text-muted-foreground truncate">
                        {chat.latestMessage.role === 'user' ? 'You: ' : ''}
                        {formatMessagePreview(chat.latestMessage.content)}
                      </p>
                    ) : (
                      <p className="text-sm text-muted-foreground italic">
                        No messages yet
                      </p>
                    )}

                    {/* Message Count Badge */}
                    {chat.messageCount > 0 && (
                      <div className="flex items-center justify-between mt-2">
                        <Badge variant="outline" className="text-xs">
                          {chat.messageCount} messages
                        </Badge>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

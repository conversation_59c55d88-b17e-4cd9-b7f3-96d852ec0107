"use client"

import { useState } from "react"
import {
  BadgeCheck,
  Bell,
  ChevronsUpDown,
  CreditCard,
  LogOut,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  User,
} from "lucide-react"

import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@/components/ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { useAuth } from "@/contexts/auth-context"
import { AuthModal } from "@/components/auth/auth-modal"
import { ModeToggleSimple } from "@/components/mode-toggle"
import { useTheme } from "next-themes"

export function NavUser() {
  const { isMobile } = useSidebar()
  const { user, isAuthenticated, logout } = useAuth()
  const { setTheme } = useTheme()
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  if (!isAuthenticated) {
    return (
      <>
        <div className="space-y-3">
          <div className="flex items-center justify-center">
            <ModeToggleSimple />
          </div>

          <Button
            onClick={() => setIsAuthModalOpen(true)}
            className="w-full h-12 bg-gradient-bestieku hover:bg-gradient-bestieku-reverse text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]"
          >
            <User className="mr-2 h-5 w-5" />
            <span className="font-medium">Mulai Sekarang</span>
          </Button>
        </div>
        <AuthModal
          isOpen={isAuthModalOpen}
          onClose={() => setIsAuthModalOpen(false)}
        />
      </>
    )
  }

  return (
    <>
      <div className="space-y-3">
        <div className="flex items-center justify-center">
          <ModeToggleSimple />
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-full p-3 rounded-xl bg-gradient-to-r from-muted/50 to-muted/30 hover:from-bestieku-primary/10 hover:to-bestieku-primary-dark/10 border border-border/50 hover:border-bestieku-primary/30 transition-all duration-200 hover:scale-[1.02] group">
              <div className="flex items-center gap-3">
                <Avatar className="h-10 w-10 ring-2 ring-bestieku-primary/20 group-hover:ring-bestieku-primary/40 transition-all duration-200">
                  <AvatarImage src={user?.image} alt={user?.name} />
                  <AvatarFallback className="bg-gradient-bestieku text-white font-semibold">
                    {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 text-left min-w-0">
                  <div className="font-medium text-sm truncate">{user?.name}</div>
                  <div className="text-xs text-muted-foreground truncate">{user?.email}</div>
                </div>
                <ChevronsUpDown className="size-4 text-muted-foreground group-hover:text-bestieku-primary transition-colors" />
              </div>
            </button>
          </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-64 rounded-xl border-border/50 shadow-xl"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={8}
            >
              <DropdownMenuLabel className="p-4 font-normal">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12 ring-2 ring-bestieku-primary/20">
                    <AvatarImage src={user?.image} alt={user?.name} />
                    <AvatarFallback className="bg-gradient-bestieku font-semibold">
                      {user?.name?.charAt(0)?.toUpperCase() || 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <div className="font-semibold truncate">{user?.name}</div>
                    <div className="text-sm text-muted-foreground truncate">{user?.email}</div>
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem className="hover:!bg-bestieku-primary/10 hover:!text-bestieku-primary focus:!bg-bestieku-primary/10 focus:!text-bestieku-primary">
                  <BadgeCheck />
                  Akun
                </DropdownMenuItem>
                <DropdownMenuItem className="hover:!bg-bestieku-primary/10 hover:!text-bestieku-primary focus:!bg-bestieku-primary/10 focus:!text-bestieku-primary">
                  <Bell />
                  Notifikasi
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem onClick={() => setTheme("light")} className="hover:!bg-bestieku-primary/10 hover:!text-bestieku-primary focus:!bg-bestieku-primary/10 focus:!text-bestieku-primary">
                  <Palette />
                  Mode Terang
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("dark")} className="hover:!bg-bestieku-primary/10 hover:!text-bestieku-primary focus:!bg-bestieku-primary/10 focus:!text-bestieku-primary">
                  <Palette />
                  Mode Gelap
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setTheme("system")} className="hover:!bg-bestieku-primary/10 hover:!text-bestieku-primary focus:!bg-bestieku-primary/10 focus:!text-bestieku-primary">
                  <Palette />
                  Tema Sistem
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={logout} className="hover:!bg-red-50 hover:!text-red-600 focus:!bg-red-50 focus:!text-red-600">
                <LogOut />
                Keluar
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
      </div>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </>
  )
}

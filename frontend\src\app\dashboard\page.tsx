'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { AppSidebar } from "@/components/app-sidebar"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Separator } from "@/components/ui/separator"
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { useAuth } from "@/contexts/auth-context"
import { CharacterCard } from "@/components/character-card"
import { CharacterSearch } from "@/components/character-search"
import { Pagination } from "@/components/pagination"
import { characterService } from "@/services/character"
import { Character, GetCharactersParams } from "@/types/character"

export default function Page() {
  const { isAuthenticated, user, isLoading } = useAuth();
  const router = useRouter();
  const [characters, setCharacters] = useState<Character[]>([]);
  const [charactersLoading, setCharactersLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchParams, setSearchParams] = useState<GetCharactersParams>({ page: 1, limit: 12 });
  const [availableTags, setAvailableTags] = useState<string[]>([]);

  // Fetch characters
  const fetchCharacters = async (params: GetCharactersParams) => {
    try {
      setCharactersLoading(true);
      const response = await characterService.getCharacters(params);
      setCharacters(response.data);
      setTotalPages(response.totalPages);
      setCurrentPage(response.currentPage);
    } catch (error) {
      console.error('Failed to fetch characters:', error);
    } finally {
      setCharactersLoading(false);
    }
  };

  // Load available tags on mount
  useEffect(() => {
    const loadTags = async () => {
      const tags = await characterService.getAllTags();
      setAvailableTags(tags);
    };
    loadTags();
  }, []);

  // Load characters on mount and when search params change
  useEffect(() => {
    fetchCharacters(searchParams);
  }, [searchParams]);

  // Handle search
  const handleSearch = (params: GetCharactersParams) => {
    const newParams = { ...searchParams, ...params };
    setSearchParams(newParams);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    const newParams = { ...searchParams, page };
    setSearchParams(newParams);
  };

  // Handle start chat
  const handleStartChat = async (characterId: string) => {
    if (!isAuthenticated) {
      alert('Please sign in to start chatting');
      return;
    }

    try {
      const chatSession = await characterService.initiateChat(characterId);
      console.log('Chat initiated:', chatSession);
      // Navigate to chat page with the new chat
      router.push(`/chat?id=${chatSession.id}`);
    } catch (error) {
      console.error('Failed to initiate chat:', error);
      alert('Failed to start chat. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <SidebarProvider>
        <AppSidebar />
        <SidebarInset>
          <div className="flex items-center justify-center h-screen">
            <div className="text-lg">Loading...</div>
          </div>
        </SidebarInset>
      </SidebarProvider>
    );
  }

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset>
        <header className="flex h-16 shrink-0 items-center gap-2 border-b">
          <div className="flex items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <Separator
              orientation="vertical"
              className="mr-2 data-[orientation=vertical]:h-4"
            />
            <Breadcrumb>
              <BreadcrumbList>
                <BreadcrumbItem className="hidden md:block">
                  <BreadcrumbLink href="/dashboard">
                    Bestieku
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="hidden md:block" />
                <BreadcrumbItem>
                  <BreadcrumbPage>Character Chat</BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
        </header>
        <div className="flex flex-1 flex-col gap-6 p-4 pt-0">
          {/* Header */}
          <div>
            <h1 className="text-2xl font-bold mb-2">
              {isAuthenticated ? `Welcome back, ${user?.name}!` : 'Welcome to Bestieku'}
            </h1>
            <p className="text-muted-foreground">
              {isAuthenticated
                ? 'Choose a character to start chatting with AI companions in immersive stories.'
                : 'Explore our AI characters and sign in to start chatting in immersive stories.'
              }
            </p>
          </div>

          {/* Search and Filters */}
          <CharacterSearch
            onSearch={handleSearch}
            isLoading={charactersLoading}
            availableTags={availableTags}
          />

          {/* Characters Grid */}
          {charactersLoading ? (
            <div className="grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4">
              {Array.from({ length: 8 }).map((_, i) => (
                <div key={i} className="bg-card border rounded-xl p-4 animate-pulse">
                  <div className="bg-muted/50 aspect-square rounded-lg mb-3" />
                  <div className="h-4 bg-muted/50 rounded mb-2" />
                  <div className="h-3 bg-muted/50 rounded mb-2" />
                  <div className="h-3 bg-muted/50 rounded w-2/3" />
                </div>
              ))}
            </div>
          ) : characters.length > 0 ? (
            <div className="grid auto-rows-min gap-4 md:grid-cols-3 lg:grid-cols-4">
              {characters.map((character) => (
                <CharacterCard
                  key={character.id}
                  character={character}
                  onStartChat={handleStartChat}
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground mb-4">No characters found matching your criteria.</p>
              <p className="text-sm text-muted-foreground">Try adjusting your search or filters.</p>
            </div>
          )}

          {/* Pagination */}
          {!charactersLoading && characters.length > 0 && (
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              isLoading={charactersLoading}
            />
          )}

          {/* Sign in CTA for non-authenticated users */}
          {!isAuthenticated && characters.length > 0 && (
            <div className="mt-8 p-6 bg-muted/30 rounded-xl text-center">
              <h2 className="text-lg font-semibold mb-2">Ready to start chatting?</h2>
              <p className="text-muted-foreground mb-4">
                Sign in to unlock personalized conversations and save your chat history.
              </p>
            </div>
          )}
        </div>
      </SidebarInset>
    </SidebarProvider>
  )
}

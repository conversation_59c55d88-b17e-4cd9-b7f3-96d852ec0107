/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/ui/sidebar.tsx":
/*!***************************************!*\
  !*** ./src/components/ui/sidebar.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar),\n/* harmony export */   SidebarContent: () => (/* binding */ SidebarContent),\n/* harmony export */   SidebarFooter: () => (/* binding */ SidebarFooter),\n/* harmony export */   SidebarGroup: () => (/* binding */ SidebarGroup),\n/* harmony export */   SidebarGroupAction: () => (/* binding */ SidebarGroupAction),\n/* harmony export */   SidebarGroupContent: () => (/* binding */ SidebarGroupContent),\n/* harmony export */   SidebarGroupLabel: () => (/* binding */ SidebarGroupLabel),\n/* harmony export */   SidebarHeader: () => (/* binding */ SidebarHeader),\n/* harmony export */   SidebarInput: () => (/* binding */ SidebarInput),\n/* harmony export */   SidebarInset: () => (/* binding */ SidebarInset),\n/* harmony export */   SidebarMenu: () => (/* binding */ SidebarMenu),\n/* harmony export */   SidebarMenuAction: () => (/* binding */ SidebarMenuAction),\n/* harmony export */   SidebarMenuBadge: () => (/* binding */ SidebarMenuBadge),\n/* harmony export */   SidebarMenuButton: () => (/* binding */ SidebarMenuButton),\n/* harmony export */   SidebarMenuItem: () => (/* binding */ SidebarMenuItem),\n/* harmony export */   SidebarMenuSkeleton: () => (/* binding */ SidebarMenuSkeleton),\n/* harmony export */   SidebarMenuSub: () => (/* binding */ SidebarMenuSub),\n/* harmony export */   SidebarMenuSubButton: () => (/* binding */ SidebarMenuSubButton),\n/* harmony export */   SidebarMenuSubItem: () => (/* binding */ SidebarMenuSubItem),\n/* harmony export */   SidebarProvider: () => (/* binding */ SidebarProvider),\n/* harmony export */   SidebarRail: () => (/* binding */ SidebarRail),\n/* harmony export */   SidebarSeparator: () => (/* binding */ SidebarSeparator),\n/* harmony export */   SidebarTrigger: () => (/* binding */ SidebarTrigger),\n/* harmony export */   useSidebar: () => (/* binding */ useSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(app-pages-browser)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_LayoutSidebar_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=LayoutSidebar!=!lucide-react */ \"(app-pages-browser)/__barrel_optimize__?names=LayoutSidebar!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _barrel_optimize_names_LayoutSidebar_lucide_react__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(_barrel_optimize_names_LayoutSidebar_lucide_react__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/use-mobile */ \"(app-pages-browser)/./src/hooks/use-mobile.ts\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/sheet */ \"(app-pages-browser)/./src/components/ui/sheet.tsx\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(app-pages-browser)/./src/components/ui/skeleton.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(app-pages-browser)/./src/components/ui/tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ Sidebar,SidebarContent,SidebarFooter,SidebarGroup,SidebarGroupAction,SidebarGroupContent,SidebarGroupLabel,SidebarHeader,SidebarInput,SidebarInset,SidebarMenu,SidebarMenuAction,SidebarMenuBadge,SidebarMenuButton,SidebarMenuItem,SidebarMenuSkeleton,SidebarMenuSub,SidebarMenuSubButton,SidebarMenuSubItem,SidebarProvider,SidebarRail,SidebarSeparator,SidebarTrigger,useSidebar auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$(), _s3 = $RefreshSig$(), _s4 = $RefreshSig$(), _s5 = $RefreshSig$(), _s6 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\";\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;\nconst SIDEBAR_WIDTH = \"16rem\";\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\";\nconst SIDEBAR_WIDTH_ICON = \"3rem\";\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\";\nconst SidebarContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.createContext(null);\nfunction useSidebar() {\n    _s();\n    const context = react__WEBPACK_IMPORTED_MODULE_1__.useContext(SidebarContext);\n    if (!context) {\n        throw new Error(\"useSidebar must be used within a SidebarProvider.\");\n    }\n    return context;\n}\n_s(useSidebar, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction SidebarProvider(param) {\n    let { defaultOpen = true, open: openProp, onOpenChange: setOpenProp, className, style, children, ...props } = param;\n    _s1();\n    const isMobile = (0,_hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile)();\n    const [openMobile, setOpenMobile] = react__WEBPACK_IMPORTED_MODULE_1__.useState(false);\n    // This is the internal state of the sidebar.\n    // We use openProp and setOpenProp for control from outside the component.\n    const [_open, _setOpen] = react__WEBPACK_IMPORTED_MODULE_1__.useState(defaultOpen);\n    const open = openProp !== null && openProp !== void 0 ? openProp : _open;\n    const setOpen = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[setOpen]\": (value)=>{\n            const openState = typeof value === \"function\" ? value(open) : value;\n            if (setOpenProp) {\n                setOpenProp(openState);\n            } else {\n                _setOpen(openState);\n            }\n            // This sets the cookie to keep the sidebar state.\n            document.cookie = \"\".concat(SIDEBAR_COOKIE_NAME, \"=\").concat(openState, \"; path=/; max-age=\").concat(SIDEBAR_COOKIE_MAX_AGE);\n        }\n    }[\"SidebarProvider.useCallback[setOpen]\"], [\n        setOpenProp,\n        open\n    ]);\n    // Helper to toggle the sidebar.\n    const toggleSidebar = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"SidebarProvider.useCallback[toggleSidebar]\": ()=>{\n            return isMobile ? setOpenMobile({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]) : setOpen({\n                \"SidebarProvider.useCallback[toggleSidebar]\": (open)=>!open\n            }[\"SidebarProvider.useCallback[toggleSidebar]\"]);\n        }\n    }[\"SidebarProvider.useCallback[toggleSidebar]\"], [\n        isMobile,\n        setOpen,\n        setOpenMobile\n    ]);\n    // Adds a keyboard shortcut to toggle the sidebar.\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect({\n        \"SidebarProvider.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"SidebarProvider.useEffect.handleKeyDown\": (event)=>{\n                    if (event.key === SIDEBAR_KEYBOARD_SHORTCUT && (event.metaKey || event.ctrlKey)) {\n                        event.preventDefault();\n                        toggleSidebar();\n                    }\n                }\n            }[\"SidebarProvider.useEffect.handleKeyDown\"];\n            window.addEventListener(\"keydown\", handleKeyDown);\n            return ({\n                \"SidebarProvider.useEffect\": ()=>window.removeEventListener(\"keydown\", handleKeyDown)\n            })[\"SidebarProvider.useEffect\"];\n        }\n    }[\"SidebarProvider.useEffect\"], [\n        toggleSidebar\n    ]);\n    // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n    // This makes it easier to style the sidebar with Tailwind classes.\n    const state = open ? \"expanded\" : \"collapsed\";\n    const contextValue = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarProvider.useMemo[contextValue]\": ()=>({\n                state,\n                open,\n                setOpen,\n                isMobile,\n                openMobile,\n                setOpenMobile,\n                toggleSidebar\n            })\n    }[\"SidebarProvider.useMemo[contextValue]\"], [\n        state,\n        open,\n        setOpen,\n        isMobile,\n        openMobile,\n        setOpenMobile,\n        toggleSidebar\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(SidebarContext.Provider, {\n        value: contextValue,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n            delayDuration: 0,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-wrapper\",\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH,\n                    \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n                    ...style\n                },\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\", className),\n                ...props,\n                children: children\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 132,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 131,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 130,\n        columnNumber: 5\n    }, this);\n}\n_s1(SidebarProvider, \"QSOkjq1AvKFJW5+zwiK52jPX7zI=\", false, function() {\n    return [\n        _hooks_use_mobile__WEBPACK_IMPORTED_MODULE_3__.useIsMobile\n    ];\n});\n_c = SidebarProvider;\nfunction Sidebar(param) {\n    let { side = \"left\", variant = \"sidebar\", collapsible = \"offcanvas\", className, children, ...props } = param;\n    _s2();\n    const { isMobile, state, openMobile, setOpenMobile } = useSidebar();\n    if (collapsible === \"none\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-slot\": \"sidebar\",\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col\", className),\n            ...props,\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 170,\n            columnNumber: 7\n        }, this);\n    }\n    if (isMobile) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.Sheet, {\n            open: openMobile,\n            onOpenChange: setOpenMobile,\n            ...props,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetContent, {\n                \"data-sidebar\": \"sidebar\",\n                \"data-slot\": \"sidebar\",\n                \"data-mobile\": \"true\",\n                className: \"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden\",\n                style: {\n                    \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE\n                },\n                side: side,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetHeader, {\n                        className: \"sr-only\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetTitle, {\n                                children: \"Sidebar\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 199,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sheet__WEBPACK_IMPORTED_MODULE_8__.SheetDescription, {\n                                children: \"Displays the mobile sidebar.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                                lineNumber: 200,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 198,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex h-full w-full flex-col\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                        lineNumber: 202,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 186,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n            lineNumber: 185,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"group peer text-sidebar-foreground hidden md:block\",\n        \"data-state\": state,\n        \"data-collapsible\": state === \"collapsed\" ? collapsible : \"\",\n        \"data-variant\": variant,\n        \"data-side\": side,\n        \"data-slot\": \"sidebar\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-gap\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear\", \"group-data-[collapsible=offcanvas]:w-0\", \"group-data-[side=right]:rotate-180\", variant === \"floating\" || variant === \"inset\" ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]\" : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon)\")\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 218,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                \"data-slot\": \"sidebar-container\",\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex\", side === \"left\" ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\" : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\", // Adjust the padding for floating and inset variants.\n                variant === \"floating\" || variant === \"inset\" ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]\" : \"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l\", className),\n                ...props,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    \"data-sidebar\": \"sidebar\",\n                    \"data-slot\": \"sidebar-inner\",\n                    className: \"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 229,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s2(Sidebar, \"hAL3+uRFwO9tnbDK50BUE5wZ71s=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c1 = Sidebar;\nfunction SidebarTrigger(param) {\n    let { className, onClick, ...props } = param;\n    _s3();\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n        \"data-sidebar\": \"trigger\",\n        \"data-slot\": \"sidebar-trigger\",\n        variant: \"ghost\",\n        size: \"icon\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"size-7\", className),\n        onClick: (event)=>{\n            onClick === null || onClick === void 0 ? void 0 : onClick(event);\n            toggleSidebar();\n        },\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_LayoutSidebar_lucide_react__WEBPACK_IMPORTED_MODULE_11__.LayoutSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 276,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"Toggle Sidebar\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 277,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 264,\n        columnNumber: 5\n    }, this);\n}\n_s3(SidebarTrigger, \"dRnjPhQbCChcVGr4xvQkpNxnqyg=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c2 = SidebarTrigger;\nfunction SidebarRail(param) {\n    let { className, ...props } = param;\n    _s4();\n    const { toggleSidebar } = useSidebar();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        \"data-sidebar\": \"rail\",\n        \"data-slot\": \"sidebar-rail\",\n        \"aria-label\": \"Toggle Sidebar\",\n        tabIndex: -1,\n        onClick: toggleSidebar,\n        title: \"Toggle Sidebar\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\", \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\", \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\", \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\", \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\", \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 286,\n        columnNumber: 5\n    }, this);\n}\n_s4(SidebarRail, \"dRnjPhQbCChcVGr4xvQkpNxnqyg=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c3 = SidebarRail;\nfunction SidebarInset(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n        \"data-slot\": \"sidebar-inset\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-background relative flex w-full flex-1 flex-col\", \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 309,\n        columnNumber: 5\n    }, this);\n}\n_c4 = SidebarInset;\nfunction SidebarInput(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_6__.Input, {\n        \"data-slot\": \"sidebar-input\",\n        \"data-sidebar\": \"input\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-background h-8 w-full shadow-none\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 326,\n        columnNumber: 5\n    }, this);\n}\n_c5 = SidebarInput;\nfunction SidebarHeader(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-header\",\n        \"data-sidebar\": \"header\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 337,\n        columnNumber: 5\n    }, this);\n}\n_c6 = SidebarHeader;\nfunction SidebarFooter(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-footer\",\n        \"data-sidebar\": \"footer\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex flex-col gap-2 p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 348,\n        columnNumber: 5\n    }, this);\n}\n_c7 = SidebarFooter;\nfunction SidebarSeparator(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_7__.Separator, {\n        \"data-slot\": \"sidebar-separator\",\n        \"data-sidebar\": \"separator\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"bg-sidebar-border mx-2 w-auto\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 362,\n        columnNumber: 5\n    }, this);\n}\n_c8 = SidebarSeparator;\nfunction SidebarContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-content\",\n        \"data-sidebar\": \"content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 373,\n        columnNumber: 5\n    }, this);\n}\n_c9 = SidebarContent;\nfunction SidebarGroup(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-group\",\n        \"data-sidebar\": \"group\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"relative flex w-full min-w-0 flex-col p-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 387,\n        columnNumber: 5\n    }, this);\n}\n_c10 = SidebarGroup;\nfunction SidebarGroupLabel(param) {\n    let { className, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"div\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-group-label\",\n        \"data-sidebar\": \"group-label\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 404,\n        columnNumber: 5\n    }, this);\n}\n_c11 = SidebarGroupLabel;\nfunction SidebarGroupAction(param) {\n    let { className, asChild = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-group-action\",\n        \"data-sidebar\": \"group-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 425,\n        columnNumber: 5\n    }, this);\n}\n_c12 = SidebarGroupAction;\nfunction SidebarGroupContent(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-group-content\",\n        \"data-sidebar\": \"group-content\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"w-full text-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 445,\n        columnNumber: 5\n    }, this);\n}\n_c13 = SidebarGroupContent;\nfunction SidebarMenu(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        \"data-slot\": \"sidebar-menu\",\n        \"data-sidebar\": \"menu\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex w-full min-w-0 flex-col gap-1\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 456,\n        columnNumber: 5\n    }, this);\n}\n_c14 = SidebarMenu;\nfunction SidebarMenuItem(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"sidebar-menu-item\",\n        \"data-sidebar\": \"menu-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/menu-item relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 467,\n        columnNumber: 5\n    }, this);\n}\n_c15 = SidebarMenuItem;\nconst sidebarMenuButtonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n            outline: \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\"\n        },\n        size: {\n            default: \"h-8 text-sm\",\n            sm: \"h-7 text-xs\",\n            lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nfunction SidebarMenuButton(param) {\n    let { asChild = false, isActive = false, variant = \"default\", size = \"default\", tooltip, className, ...props } = param;\n    _s5();\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    const { isMobile, state } = useSidebar();\n    const button = /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-button\",\n        \"data-sidebar\": \"menu-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(sidebarMenuButtonVariants({\n            variant,\n            size\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 515,\n        columnNumber: 5\n    }, this);\n    if (!tooltip) {\n        return button;\n    }\n    if (typeof tooltip === \"string\") {\n        tooltip = {\n            children: tooltip\n        };\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                asChild: true,\n                children: button\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 537,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                side: \"right\",\n                align: \"center\",\n                hidden: state !== \"collapsed\" || isMobile,\n                ...tooltip\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 536,\n        columnNumber: 5\n    }, this);\n}\n_s5(SidebarMenuButton, \"DSCdbs8JtpmKVxCYgM7sPAZNgB0=\", false, function() {\n    return [\n        useSidebar\n    ];\n});\n_c16 = SidebarMenuButton;\nfunction SidebarMenuAction(param) {\n    let { className, asChild = false, showOnHover = false, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-action\",\n        \"data-sidebar\": \"menu-action\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\", // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", showOnHover && \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 560,\n        columnNumber: 5\n    }, this);\n}\n_c17 = SidebarMenuAction;\nfunction SidebarMenuBadge(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-menu-badge\",\n        \"data-sidebar\": \"menu-badge\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\", \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\", \"peer-data-[size=sm]/menu-button:top-1\", \"peer-data-[size=default]/menu-button:top-1.5\", \"peer-data-[size=lg]/menu-button:top-2.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 585,\n        columnNumber: 5\n    }, this);\n}\n_c18 = SidebarMenuBadge;\nfunction SidebarMenuSkeleton(param) {\n    let { className, showIcon = false, ...props } = param;\n    _s6();\n    // Random width between 50 to 90%.\n    const width = react__WEBPACK_IMPORTED_MODULE_1__.useMemo({\n        \"SidebarMenuSkeleton.useMemo[width]\": ()=>{\n            return \"\".concat(Math.floor(Math.random() * 40) + 50, \"%\");\n        }\n    }[\"SidebarMenuSkeleton.useMemo[width]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        \"data-slot\": \"sidebar-menu-skeleton\",\n        \"data-sidebar\": \"menu-skeleton\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"flex h-8 items-center gap-2 rounded-md px-2\", className),\n        ...props,\n        children: [\n            showIcon && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"size-4 rounded-md\",\n                \"data-sidebar\": \"menu-skeleton-icon\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 622,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_9__.Skeleton, {\n                className: \"h-4 max-w-(--skeleton-width) flex-1\",\n                \"data-sidebar\": \"menu-skeleton-text\",\n                style: {\n                    \"--skeleton-width\": width\n                }\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 615,\n        columnNumber: 5\n    }, this);\n}\n_s6(SidebarMenuSkeleton, \"nKFjX4dxbYo91VAj5VdWQ1XUe3I=\");\n_c19 = SidebarMenuSkeleton;\nfunction SidebarMenuSub(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n        \"data-slot\": \"sidebar-menu-sub\",\n        \"data-sidebar\": \"menu-sub\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 642,\n        columnNumber: 5\n    }, this);\n}\n_c20 = SidebarMenuSub;\nfunction SidebarMenuSubItem(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"sidebar-menu-sub-item\",\n        \"data-sidebar\": \"menu-sub-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"group/menu-sub-item relative\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 660,\n        columnNumber: 5\n    }, this);\n}\n_c21 = SidebarMenuSubItem;\nfunction SidebarMenuSubButton(param) {\n    let { asChild = false, size = \"md\", isActive = false, className, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_12__.Slot : \"a\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"sidebar-menu-sub-button\",\n        \"data-sidebar\": \"menu-sub-button\",\n        \"data-size\": size,\n        \"data-active\": isActive,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(\"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline-hidden focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\", \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\", size === \"sm\" && \"text-xs\", size === \"md\" && \"text-sm\", \"group-data-[collapsible=icon]:hidden\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\sidebar.tsx\",\n        lineNumber: 683,\n        columnNumber: 5\n    }, this);\n}\n_c22 = SidebarMenuSubButton;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22;\n$RefreshReg$(_c, \"SidebarProvider\");\n$RefreshReg$(_c1, \"Sidebar\");\n$RefreshReg$(_c2, \"SidebarTrigger\");\n$RefreshReg$(_c3, \"SidebarRail\");\n$RefreshReg$(_c4, \"SidebarInset\");\n$RefreshReg$(_c5, \"SidebarInput\");\n$RefreshReg$(_c6, \"SidebarHeader\");\n$RefreshReg$(_c7, \"SidebarFooter\");\n$RefreshReg$(_c8, \"SidebarSeparator\");\n$RefreshReg$(_c9, \"SidebarContent\");\n$RefreshReg$(_c10, \"SidebarGroup\");\n$RefreshReg$(_c11, \"SidebarGroupLabel\");\n$RefreshReg$(_c12, \"SidebarGroupAction\");\n$RefreshReg$(_c13, \"SidebarGroupContent\");\n$RefreshReg$(_c14, \"SidebarMenu\");\n$RefreshReg$(_c15, \"SidebarMenuItem\");\n$RefreshReg$(_c16, \"SidebarMenuButton\");\n$RefreshReg$(_c17, \"SidebarMenuAction\");\n$RefreshReg$(_c18, \"SidebarMenuBadge\");\n$RefreshReg$(_c19, \"SidebarMenuSkeleton\");\n$RefreshReg$(_c20, \"SidebarMenuSub\");\n$RefreshReg$(_c21, \"SidebarMenuSubItem\");\n$RefreshReg$(_c22, \"SidebarMenuSubButton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/sidebar.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/__barrel_optimize__?names=LayoutSidebar!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!******************************************************************************************************!*\
  !*** __barrel_optimize__?names=LayoutSidebar!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \******************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {



;
    // Wrapped in an IIFE to avoid polluting the global scope
    ;
    (function () {
        var _a, _b;
        // Legacy CSS implementations will `eval` browser code in a Node.js context
        // to extract CSS. For backwards compatibility, we need to check we're in a
        // browser context before continuing.
        if (typeof self !== 'undefined' &&
            // AMP / No-JS mode does not inject these helpers:
            '$RefreshHelpers$' in self) {
            // @ts-ignore __webpack_module__ is global
            var currentExports = module.exports;
            // @ts-ignore __webpack_module__ is global
            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;
            // This cannot happen in MainTemplate because the exports mismatch between
            // templating and execution.
            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);
            // A module can be accepted automatically based on its exports, e.g. when
            // it is a Refresh Boundary.
            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {
                // Save the previous exports signature on update so we can compare the boundary
                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)
                module.hot.dispose(function (data) {
                    data.prevSignature =
                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);
                });
                // Unconditionally accept an update to this module, we'll check if it's
                // still a Refresh Boundary later.
                // @ts-ignore importMeta is replaced in the loader
                module.hot.accept();
                // This field is set when the previous version of this module was a
                // Refresh Boundary, letting us know we need to check for invalidation or
                // enqueue an update.
                if (prevSignature !== null) {
                    // A boundary can become ineligible if its exports are incompatible
                    // with the previous exports.
                    //
                    // For example, if you add/remove/change exports, we'll want to
                    // re-execute the importing modules, and force those components to
                    // re-render. Similarly, if you convert a class component to a
                    // function, we want to invalidate the boundary.
                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {
                        module.hot.invalidate();
                    }
                    else {
                        self.$RefreshHelpers$.scheduleUpdate();
                    }
                }
            }
            else {
                // Since we just executed the code for the module, it's possible that the
                // new exports made it ineligible for being a boundary.
                // We only care about the case when we were _previously_ a boundary,
                // because we already accepted this update (accidental side effect).
                var isNoLongerABoundary = prevSignature !== null;
                if (isNoLongerABoundary) {
                    module.hot.invalidate();
                }
            }
        }
    })();


/***/ })

});
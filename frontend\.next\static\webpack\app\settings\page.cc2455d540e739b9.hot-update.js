"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/settings/page",{

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js":
/*!**************************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/ellipsis.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Ellipsis)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"1\",\n            key: \"41hilf\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"19\",\n            cy: \"12\",\n            r: \"1\",\n            key: \"1wjl8i\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"5\",\n            cy: \"12\",\n            r: \"1\",\n            key: \"1pcz8c\"\n        }\n    ]\n];\nconst Ellipsis = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ellipsis\", __iconNode);\n //# sourceMappingURL=ellipsis.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/settings/page.tsx":
/*!***********************************!*\
  !*** ./src/app/settings/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SettingsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_profile_profile_form__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/profile/profile-form */ \"(app-pages-browser)/./src/components/profile/profile-form.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Palette,Settings,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Palette,Settings,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Palette,Settings,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Palette,Settings,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,Palette,Settings,Shield,User!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/palette.js\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction SettingsPage() {\n    _s();\n    const { isAuthenticated, user } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const [isAuthModalOpen, setIsAuthModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarInset, {\n                    className: \"flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarTrigger, {\n                                        className: \"-ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 33,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_9__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 34,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.Breadcrumb, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbItem, {\n                                                    className: \"hidden md:block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbLink, {\n                                                        href: \"/dashboard\",\n                                                        children: \"Bestieku\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 41,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 40,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbSeparator, {\n                                                    className: \"hidden md:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 45,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_8__.BreadcrumbPage, {\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 47,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 46,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 32,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 flex-col gap-4 p-4 pt-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-2xl mx-auto text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] rounded-full flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"w-12 h-12 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 59,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 58,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-3xl font-bold mb-4\",\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-muted-foreground text-lg\",\n                                                    children: \"Please sign in to access your account settings and manage your profile.\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 62,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                            onClick: ()=>setIsAuthModalOpen(true),\n                                            className: \"h-12 px-8 bg-gradient-to-r from-[#2DD4BF] to-[#14B8A6] hover:from-[#14B8A6] hover:to-[#0D9488] text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"mr-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Sign In to Continue\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                            lineNumber: 67,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 30,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: isAuthModalOpen,\n                    onClose: ()=>setIsAuthModalOpen(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n            lineNumber: 28,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_3__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_2__.SidebarInset, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-1 flex-col gap-4 p-4 pt-0\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"min-h-[100vh] flex-1 rounded-xl bg-muted/50 md:min-h-min\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 sticky top-0 z-10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-[#2DD4BF] to-[#14B8A6] rounded-xl flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 98,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold\",\n                                                        children: \"Settings\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 101,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground\",\n                                                        children: \"Manage your account settings and preferences\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 100,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 94,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-4xl mx-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 lg:grid-cols-4 gap-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg bg-[#2DD4BF]/10 border border-[#2DD4BF]/20\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-[#2DD4BF] rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-white\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 121,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 120,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-[#2DD4BF]\",\n                                                                            children: \"Profile\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 123,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 119,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 118,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer opacity-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-muted rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 130,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 129,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"Privacy\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 132,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 128,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 127,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer opacity-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-muted rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 139,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 138,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"Notifications\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 141,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 137,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 136,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer opacity-50\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center gap-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-8 h-8 bg-muted rounded-lg flex items-center justify-center\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_Palette_Settings_Shield_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-muted-foreground\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                                lineNumber: 148,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 147,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-muted-foreground\",\n                                                                            children: \"Appearance\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                            lineNumber: 150,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 146,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 145,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 117,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"mt-4 p-3 rounded-lg bg-muted/30 border border-dashed border-muted-foreground/30\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs text-muted-foreground text-center\",\n                                                            children: \"More settings coming soon\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 156,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 116,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"lg:col-span-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-background rounded-xl border shadow-sm\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6 border-b\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                                    className: \"text-xl font-semibold\",\n                                                                    children: \"Profile Settings\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 166,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-muted-foreground mt-1\",\n                                                                    children: \"Update your personal information and profile picture\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                    lineNumber: 167,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-6\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_profile_profile_form__WEBPACK_IMPORTED_MODULE_4__.ProfileForm, {}, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                                lineNumber: 173,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                            lineNumber: 172,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                                lineNumber: 163,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                        lineNumber: 92,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\settings\\\\page.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(SettingsPage, \"1nKlJCbdft6DIYaqDJORLJwcu3w=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth\n    ];\n});\n_c = SettingsPage;\nvar _c;\n$RefreshReg$(_c, \"SettingsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/settings/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ui/breadcrumb.tsx":
/*!******************************************!*\
  !*** ./src/components/ui/breadcrumb.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Breadcrumb: () => (/* binding */ Breadcrumb),\n/* harmony export */   BreadcrumbEllipsis: () => (/* binding */ BreadcrumbEllipsis),\n/* harmony export */   BreadcrumbItem: () => (/* binding */ BreadcrumbItem),\n/* harmony export */   BreadcrumbLink: () => (/* binding */ BreadcrumbLink),\n/* harmony export */   BreadcrumbList: () => (/* binding */ BreadcrumbList),\n/* harmony export */   BreadcrumbPage: () => (/* binding */ BreadcrumbPage),\n/* harmony export */   BreadcrumbSeparator: () => (/* binding */ BreadcrumbSeparator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(app-pages-browser)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight,MoreHorizontal!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n\n\n\n\n\nfunction Breadcrumb(param) {\n    let { ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        \"aria-label\": \"breadcrumb\",\n        \"data-slot\": \"breadcrumb\",\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 8,\n        columnNumber: 10\n    }, this);\n}\n_c = Breadcrumb;\nfunction BreadcrumbList(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n        \"data-slot\": \"breadcrumb-list\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-muted-foreground flex flex-wrap items-center gap-1.5 text-sm break-words sm:gap-2.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n_c1 = BreadcrumbList;\nfunction BreadcrumbItem(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"breadcrumb-item\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"inline-flex items-center gap-1.5\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n_c2 = BreadcrumbItem;\nfunction BreadcrumbLink(param) {\n    let { asChild, className, ...props } = param;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : \"a\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        \"data-slot\": \"breadcrumb-link\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"hover:text-foreground transition-colors\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_c3 = BreadcrumbLink;\nfunction BreadcrumbPage(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"breadcrumb-page\",\n        role: \"link\",\n        \"aria-disabled\": \"true\",\n        \"aria-current\": \"page\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-foreground font-normal\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n_c4 = BreadcrumbPage;\nfunction BreadcrumbSeparator(param) {\n    let { children, className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        \"data-slot\": \"breadcrumb-separator\",\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"[&>svg]:size-3.5\", className),\n        ...props,\n        children: children !== null && children !== void 0 ? children : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n            lineNumber: 78,\n            columnNumber: 20\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, this);\n}\n_c5 = BreadcrumbSeparator;\nfunction BreadcrumbEllipsis(param) {\n    let { className, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        \"data-slot\": \"breadcrumb-ellipsis\",\n        role: \"presentation\",\n        \"aria-hidden\": \"true\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex size-9 items-center justify-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_MoreHorizontal_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                className: \"size-4\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 95,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"sr-only\",\n                children: \"More\"\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\ui\\\\breadcrumb.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_c6 = BreadcrumbEllipsis;\n\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"Breadcrumb\");\n$RefreshReg$(_c1, \"BreadcrumbList\");\n$RefreshReg$(_c2, \"BreadcrumbItem\");\n$RefreshReg$(_c3, \"BreadcrumbLink\");\n$RefreshReg$(_c4, \"BreadcrumbPage\");\n$RefreshReg$(_c5, \"BreadcrumbSeparator\");\n$RefreshReg$(_c6, \"BreadcrumbEllipsis\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ui/breadcrumb.tsx\n"));

/***/ })

});
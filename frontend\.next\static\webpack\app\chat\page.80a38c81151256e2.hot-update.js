"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst MessageContent = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, undefined);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, undefined);\n});\n_c = MessageContent;\nconst MessageItem = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s((param)=>{\n    let { message, characterImage, characterName } = param;\n    var _characterName_charAt;\n    _s();\n    const formatTime = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"MessageItem.useCallback[formatTime]\": (dateString)=>{\n            try {\n                return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                    addSuffix: true\n                });\n            } catch (e) {\n                return '';\n            }\n        }\n    }[\"MessageItem.useCallback[formatTime]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n        children: [\n            message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: message.content,\n                        role: message.role\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs\",\n                                children: formatTime(message.createdAt)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, undefined),\n                            message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 104,\n        columnNumber: 5\n    }, undefined);\n}, \"HvJHf3nLs/NeKKNMBW1x3UPtyJE=\"));\n_c1 = MessageItem;\nconst StreamingMessage = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { streamingMessage, characterImage, characterName } = param;\n    var _characterName_charAt;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex items-end gap-2 justify-start\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                className: \"w-8 h-8 mb-1\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                        src: characterImage,\n                        alt: characterName\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 153,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                        children: (characterName === null || characterName === void 0 ? void 0 : (_characterName_charAt = characterName.charAt(0)) === null || _characterName_charAt === void 0 ? void 0 : _characterName_charAt.toUpperCase()) || 'C'\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                children: [\n                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                        content: streamingMessage,\n                        role: \"assistant\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: \"Thinking\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 164,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex space-x-1 ml-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.1s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                        style: {\n                                            animationDelay: '0.2s'\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 165,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 163,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center mt-2 text-muted-foreground\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs\",\n                                    children: \"Typing...\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 159,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 151,\n        columnNumber: 5\n    }, undefined);\n});\n_c2 = StreamingMessage;\nconst ChatInput = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_s1((param)=>{\n    let { onSendMessage, disabled, characterName, isStreaming } = param;\n    _s1();\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Use ref to store current message to avoid stale closures\n    const messageRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)('');\n    messageRef.current = newMessage;\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleSendMessage]\": ()=>{\n            const currentMessage = messageRef.current.trim();\n            if (!currentMessage || disabled) return;\n            onSendMessage(currentMessage);\n            setNewMessage('');\n            messageRef.current = '';\n        }\n    }[\"ChatInput.useCallback[handleSendMessage]\"], [\n        disabled,\n        onSendMessage\n    ]);\n    const handleKeyPress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleKeyPress]\": (e)=>{\n            if (e.key === 'Enter' && !e.shiftKey) {\n                e.preventDefault();\n                handleSendMessage();\n            }\n        }\n    }[\"ChatInput.useCallback[handleKeyPress]\"], [\n        handleSendMessage\n    ]);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInput.useCallback[handleInputChange]\": (e)=>{\n            const value = e.target.value;\n            setNewMessage(value);\n            messageRef.current = value;\n        }\n    }[\"ChatInput.useCallback[handleInputChange]\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-end space-x-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                ref: inputRef,\n                                value: newMessage,\n                                onChange: handleInputChange,\n                                onKeyPress: handleKeyPress,\n                                placeholder: \"Message \".concat(characterName || 'character', \"...\"),\n                                disabled: disabled,\n                                className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 224,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                children: disabled && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: handleSendMessage,\n                        disabled: !newMessage.trim() || disabled,\n                        className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 241,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 222,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Press Enter to send, Shift+Enter for new line\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, undefined),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-[#2DD4BF] animate-pulse\",\n                        children: \"AI is responding...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 221,\n        columnNumber: 5\n    }, undefined);\n}, \"1o5B5G/jtGrG8z8Ow45dQHalZdE=\"));\n_c3 = ChatInput;\nconst ChatInterface = /*#__PURE__*/ _s2((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c4 = _s2((param)=>{\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character6;\n    _s2();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    // Cleanup streaming timeout\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    // Stable scroll function that doesn't cause re-renders\n    const scrollToBottomRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)();\n    scrollToBottomRef.current = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    // Only scroll when messages count changes, not on every render\n    const messagesCountRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(messages.length);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (messages.length > messagesCountRef.current) {\n                messagesCountRef.current = messages.length;\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 100);\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            messagesCountRef.current = messages.length;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages.length\n    ]);\n    // Separate effect for streaming - only scroll when streaming starts or ends\n    const isStreamingRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(isStreaming);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            if (isStreaming && !isStreamingRef.current) {\n                // Streaming just started\n                const timeoutId = setTimeout({\n                    \"ChatInterface.useEffect.timeoutId\": ()=>{\n                        var _scrollToBottomRef_current;\n                        (_scrollToBottomRef_current = scrollToBottomRef.current) === null || _scrollToBottomRef_current === void 0 ? void 0 : _scrollToBottomRef_current.call(scrollToBottomRef);\n                    }\n                }[\"ChatInterface.useEffect.timeoutId\"], 200);\n                isStreamingRef.current = isStreaming;\n                return ({\n                    \"ChatInterface.useEffect\": ()=>clearTimeout(timeoutId)\n                })[\"ChatInterface.useEffect\"];\n            }\n            isStreamingRef.current = isStreaming;\n        }\n    }[\"ChatInterface.useEffect\"], [\n        isStreaming\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSendMessage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ChatInterface.useCallback[handleSendMessage]\": async (messageText)=>{\n            if (!messageText.trim() || sending) return;\n            const tempId = \"temp-\".concat(Date.now());\n            setSending(true);\n            try {\n                // Add user message to UI immediately\n                const userMessage = {\n                    id: tempId,\n                    role: 'user',\n                    content: messageText,\n                    contentType: 'text',\n                    createdAt: new Date().toISOString(),\n                    updatedAt: new Date().toISOString()\n                };\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>[\n                            ...prev,\n                            userMessage\n                        ]\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                console.log('Sending message to chat:', chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                // Send message with streaming enabled\n                const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                    message: messageText,\n                    streaming: true\n                });\n                console.log('Message sent successfully:', response);\n                // Update the temporary message with real ID if available\n                if (response.id) {\n                    setMessages({\n                        \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.map({\n                                \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id === tempId ? {\n                                        ...msg,\n                                        id: response.id\n                                    } : msg\n                            }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                    }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                }\n                // Start streaming response\n                await startStreaming();\n            } catch (error) {\n                console.error('Failed to send message:', error);\n                // Remove the temporary user message on error\n                setMessages({\n                    \"ChatInterface.useCallback[handleSendMessage]\": (prev)=>prev.filter({\n                            \"ChatInterface.useCallback[handleSendMessage]\": (msg)=>msg.id !== tempId\n                        }[\"ChatInterface.useCallback[handleSendMessage]\"])\n                }[\"ChatInterface.useCallback[handleSendMessage]\"]);\n                alert('Failed to send message. Please try again.');\n            } finally{\n                setSending(false);\n            }\n        }\n    }[\"ChatInterface.useCallback[handleSendMessage]\"], [\n        sending,\n        chat.id\n    ]);\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        // More aggressive throttling for streaming message updates\n        let lastUpdateTime = 0;\n        const updateStreamingMessage = (message)=>{\n            const now = Date.now();\n            if (streamingTimeoutRef.current) {\n                clearTimeout(streamingTimeoutRef.current);\n            }\n            // Immediate update if it's been more than 100ms since last update\n            if (now - lastUpdateTime > 100) {\n                setStreamingMessage(message);\n                lastUpdateTime = now;\n            } else {\n                // Otherwise, throttle the update\n                streamingTimeoutRef.current = setTimeout(()=>{\n                    setStreamingMessage(message);\n                    lastUpdateTime = Date.now();\n                }, 100);\n            }\n        };\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    updateStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Clear any pending streaming updates\n                    if (streamingTimeoutRef.current) {\n                        clearTimeout(streamingTimeoutRef.current);\n                    }\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 499,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 500,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 498,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 497,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 511,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 512,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 510,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 508,\n                            columnNumber: 13\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 496,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 529,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 528,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 535,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 538,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 540,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 539,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 537,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 526,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 548,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 547,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 546,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 525,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 524,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message)=>{\n                        var _chat_character, _chat_character1;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageItem, {\n                            message: message,\n                            characterImage: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                            characterName: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                        }, message.id, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, undefined);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(StreamingMessage, {\n                        streamingMessage: streamingMessage,\n                        characterImage: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                        characterName: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 567,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatInput, {\n                onSendMessage: handleSendMessage,\n                disabled: sending || isStreaming,\n                characterName: (_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : _chat_character6.name,\n                isStreaming: isStreaming\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 522,\n        columnNumber: 5\n    }, undefined);\n}, \"vgeNZ7VOK4tqoaT5U0AjfUXl1wo=\")), \"vgeNZ7VOK4tqoaT5U0AjfUXl1wo=\");\n_c5 = ChatInterface;\nChatInterface.displayName = 'ChatInterface';\n\nvar _c, _c1, _c2, _c3, _c4, _c5;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"MessageItem\");\n$RefreshReg$(_c2, \"StreamingMessage\");\n$RefreshReg$(_c3, \"ChatInput\");\n$RefreshReg$(_c4, \"ChatInterface$memo\");\n$RefreshReg$(_c5, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/chat-list */ \"(app-pages-browser)/./src/components/chat/chat-list.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/character-profile-sidebar */ \"(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Menu,MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPageContent() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatListOpen, setIsChatListOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isMobileProfileOpen, setIsMobileProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = searchParams.get('id');\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatPageContent.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatPageContent.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatPageContent.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatPageContent.useEffect\"];\n        }\n    }[\"ChatPageContent.useEffect\"], []);\n    // Mobile behavior: close chat list when chat is selected, close profile by default\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (isMobile) {\n                if (selectedChat) {\n                    setIsChatListOpen(false);\n                }\n                setIsProfileOpen(false);\n            } else {\n                setIsChatListOpen(true);\n                setIsProfileOpen(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isMobile,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                // Show auth modal instead of redirecting\n                setShowAuthModal(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    // Handle chats loaded callback\n    const handleChatsLoaded = (chats)=>{\n        console.log('Chats loaded:', chats);\n        // Auto-select chat from URL if available\n        if (chatId && !selectedChat) {\n            const targetChat = chats.find((chat)=>chat.id === chatId);\n            if (targetChat) {\n                console.log('Auto-selecting chat from URL:', targetChat);\n                setSelectedChat(targetChat);\n            } else {\n                console.log('Chat not found in loaded chats:', chatId);\n                // Remove invalid chat ID from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('id');\n                window.history.replaceState({}, '', newUrl.toString());\n            }\n        }\n    };\n    const handleChatSelect = (chat)=>{\n        setSelectedChat(chat);\n        // Update URL with chat ID\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.set('id', chat.id);\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, close chat list when chat is selected\n        if (isMobile) {\n            setIsChatListOpen(false);\n        }\n    };\n    const handleBackToList = ()=>{\n        setSelectedChat(null);\n        // Remove chat ID from URL\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.delete('id');\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, show chat list when going back\n        if (isMobile) {\n            setIsChatListOpen(true);\n        }\n    };\n    // Toggle chat list (for mobile)\n    const toggleChatList = ()=>{\n        setIsChatListOpen(!isChatListOpen);\n    };\n    // Toggle mobile profile\n    const toggleMobileProfile = ()=>{\n        setIsMobileProfileOpen(!isMobileProfileOpen);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 129,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                    className: \"flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                        className: \"-ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    className: \"hidden md:block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                        href: \"/dashboard\",\n                                                        children: \"Bestieku\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                    className: \"hidden md:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 157,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                        children: \"Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 159,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 158,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 150,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center max-w-md mx-auto p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Selamat Datang di Bestieku Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-6\",\n                                            children: \"Untuk mulai mengobrol dengan karakter AI favorit Anda, silakan masuk atau daftar terlebih dahulu.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 172,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAuthModal(true),\n                                            className: \"inline-flex items-center px-6 py-3 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors font-medium\",\n                                            children: \"Masuk / Daftar\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 175,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 168,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 187,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 140,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 197,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                className: \"flex flex-col h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4 w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                    className: \"flex-1\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                    children: \"Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                isMobile && selectedChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: toggleChatList,\n                                    className: \"p-2 hover:bg-muted rounded-lg transition-colors md:hidden\",\n                                    \"aria-label\": \"Toggle chat list\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 min-h-0 relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n            \".concat(isMobile ? \"absolute inset-y-0 left-0 z-20 w-full bg-background transform transition-transform duration-300 \".concat(isChatListOpen ? 'translate-x-0' : '-translate-x-full') : 'w-80 border-r bg-background', \"\\n            flex flex-col min-h-0\\n          \"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__.ChatList, {\n                                    selectedChatId: selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id,\n                                    onChatSelect: handleChatSelect,\n                                    onChatsLoaded: handleChatsLoaded\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"\\n            \".concat(isMobile ? 'flex-1' : 'flex-1', \"\\n            flex min-h-0\\n          \"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col min-h-0\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__.ChatInterface, {\n                                            chat: selectedChat,\n                                            onBack: handleBackToList,\n                                            onToggleProfile: isMobile ? toggleMobileProfile : undefined\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center max-w-md mx-auto p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: \"Welcome to Bestieku Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6\",\n                                                        children: isMobile ? \"Tap the menu to select a chat or browse characters to start a new conversation.\" : \"Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 269,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-3\",\n                                                        children: [\n                                                            isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: toggleChatList,\n                                                                className: \"inline-flex items-center px-4 py-2 bg-muted hover:bg-muted/80 text-foreground rounded-lg transition-colors w-full justify-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Menu_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                        lineNumber: 281,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"View Chats\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>router.push('/dashboard'),\n                                                                className: \"inline-flex items-center px-4 py-2 bg-bestieku-primary hover:bg-bestieku-primary-dark rounded-lg transition-colors w-full justify-center\",\n                                                                children: \"Browse Characters\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                                lineNumber: 285,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 265,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedChat && !isMobile && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 min-h-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: isProfileOpen,\n                                            onToggle: ()=>setIsProfileOpen(!isProfileOpen)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 300,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            isMobile && isChatListOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-black/50 z-10\",\n                                onClick: ()=>setIsChatListOpen(false)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            isMobile && selectedChat && isMobileProfileOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-black/50 z-30\",\n                                        onClick: ()=>setIsMobileProfileOpen(false)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-y-0 right-0 z-40 w-80 max-w-[90vw] bg-background transform transition-transform duration-300\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: true,\n                                            onToggle: ()=>setIsMobileProfileOpen(false)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 196,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPageContent, \"n1Upz5lLmgTMa9Nx1UW6sF9Flws=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPageContent;\nfunction ChatPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-bestieku-primary mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 346,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 345,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 344,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatPageContent, {}, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 351,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 343,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChatPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPageContent\");\n$RefreshReg$(_c1, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});
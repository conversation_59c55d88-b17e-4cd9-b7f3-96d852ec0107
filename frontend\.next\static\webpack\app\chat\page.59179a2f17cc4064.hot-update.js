"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/components/chat/chat-interface.tsx":
/*!************************************************!*\
  !*** ./src/components/chat/chat-interface.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatInterface: () => (/* binding */ ChatInterface)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _services_chat__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/services/chat */ \"(app-pages-browser)/./src/services/chat.ts\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/ellipsis-vertical.js\");\n/* harmony import */ var _barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=MoreVertical,Send!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/send.js\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-markdown */ \"(app-pages-browser)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! remark-gfm */ \"(app-pages-browser)/./node_modules/remark-gfm/lib/index.js\");\n/* __next_internal_client_entry_do_not_use__ ChatInterface auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction MessageContent(param) {\n    let { content, role } = param;\n    if (role === 'user') {\n        // For user messages, just display as plain text with line breaks\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n            className: \"text-sm whitespace-pre-wrap leading-relaxed\",\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 27,\n            columnNumber: 12\n        }, this);\n    }\n    // For AI messages, render as Markdown\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-sm leading-relaxed prose prose-sm max-w-none dark:prose-invert\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_6__.Markdown, {\n            remarkPlugins: [\n                remark_gfm__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n            ],\n            components: {\n                // Customize paragraph styling\n                p: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-2 last:mb-0 leading-relaxed\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize emphasis (italic)\n                em: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                        className: \"italic text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 42,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize strong (bold)\n                strong: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        className: \"font-semibold text-inherit\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize code blocks\n                code: (param)=>{\n                    let { children, className } = param;\n                    const isInline = !className;\n                    if (isInline) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                            className: \"bg-muted px-1.5 py-0.5 rounded text-xs font-mono\",\n                            children: children\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 17\n                        }, void 0);\n                    }\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                        className: \"block bg-muted p-3 rounded-lg text-xs font-mono overflow-x-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 59,\n                        columnNumber: 15\n                    }, void 0);\n                },\n                // Customize lists\n                ul: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 66,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                ol: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                        className: \"list-decimal list-inside mb-2 space-y-1\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                li: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        className: \"text-sm\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, void 0);\n                },\n                // Customize blockquotes\n                blockquote: (param)=>{\n                    let { children } = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                        className: \"border-l-4 border-muted pl-4 italic my-2\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, void 0);\n                }\n            },\n            children: content\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n_c = MessageContent;\nfunction ChatInterface(param) {\n    let { chat, onBack } = param;\n    var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2, _chat_character3, _chat_character4, _chat_character5, _chat_character_name_charAt1, _chat_character_name1, _chat_character6, _chat_character7;\n    _s();\n    const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sending, setSending] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [newMessage, setNewMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [streamingMessage, setStreamingMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isStreaming, setIsStreaming] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const streamConnectionRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            loadMessages();\n            return ({\n                \"ChatInterface.useEffect\": ()=>{\n                    // Cleanup stream connection on unmount\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                }\n            })[\"ChatInterface.useEffect\"];\n        }\n    }[\"ChatInterface.useEffect\"], [\n        chat.id\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatInterface.useEffect\": ()=>{\n            scrollToBottom();\n        }\n    }[\"ChatInterface.useEffect\"], [\n        messages,\n        streamingMessage\n    ]);\n    const loadMessages = async ()=>{\n        try {\n            setLoading(true);\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.getChatMessages(chat.id, {\n                limit: 50\n            });\n            setMessages(response.data.reverse()); // Reverse to show oldest first\n        } catch (error) {\n            console.error('Failed to load messages:', error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const scrollToBottom = ()=>{\n        var _messagesEndRef_current;\n        (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n            behavior: 'smooth'\n        });\n    };\n    const handleSendMessage = async ()=>{\n        if (!newMessage.trim() || sending) return;\n        const messageText = newMessage.trim();\n        const tempId = \"temp-\".concat(Date.now());\n        setNewMessage('');\n        setSending(true);\n        try {\n            // Add user message to UI immediately\n            const userMessage = {\n                id: tempId,\n                role: 'user',\n                content: messageText,\n                contentType: 'text',\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString()\n            };\n            setMessages((prev)=>[\n                    ...prev,\n                    userMessage\n                ]);\n            console.log('Sending message to chat:', chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            // Send message with streaming enabled\n            const response = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.sendMessage(chat.id, {\n                message: messageText,\n                streaming: true\n            });\n            console.log('Message sent successfully:', response);\n            // Update the temporary message with real ID if available\n            if (response.id) {\n                setMessages((prev)=>prev.map((msg)=>msg.id === tempId ? {\n                            ...msg,\n                            id: response.id\n                        } : msg));\n            }\n            // Start streaming response\n            await startStreaming();\n        } catch (error) {\n            console.error('Failed to send message:', error);\n            // Remove the temporary user message on error\n            setMessages((prev)=>prev.filter((msg)=>msg.id !== tempId));\n            alert('Failed to send message. Please try again.');\n        } finally{\n            setSending(false);\n        }\n    };\n    const startStreaming = async ()=>{\n        console.log('Starting stream for chat:', chat.id);\n        setIsStreaming(true);\n        setStreamingMessage('');\n        // Close existing connection\n        if (streamConnectionRef.current) {\n            streamConnectionRef.current.close();\n        }\n        let currentStreamingMessage = '';\n        const onMessage = (data)=>{\n            console.log('Received SSE event:', data);\n            switch(data.event){\n                case 'start':\n                    console.log('Stream started');\n                    currentStreamingMessage = '';\n                    setStreamingMessage('');\n                    break;\n                case 'token':\n                    currentStreamingMessage += data.data;\n                    setStreamingMessage(currentStreamingMessage);\n                    break;\n                case 'metadata':\n                    console.log('Stream metadata:', data.data);\n                    break;\n                case 'end':\n                    var _data_data;\n                    console.log('Stream ended, final message:', currentStreamingMessage);\n                    // Finalize the streaming message\n                    const finalMessage = {\n                        id: ((_data_data = data.data) === null || _data_data === void 0 ? void 0 : _data_data.chatMessageId) || \"msg-\".concat(Date.now()),\n                        role: 'assistant',\n                        content: currentStreamingMessage,\n                        contentType: 'text',\n                        createdAt: new Date().toISOString(),\n                        updatedAt: new Date().toISOString()\n                    };\n                    setMessages((prev)=>[\n                            ...prev,\n                            finalMessage\n                        ]);\n                    setStreamingMessage('');\n                    setIsStreaming(false);\n                    if (streamConnectionRef.current) {\n                        streamConnectionRef.current.close();\n                    }\n                    break;\n                default:\n                    console.log('Unknown SSE event:', data.event);\n            }\n        };\n        const onError = async (error)=>{\n            console.error('Stream Error:', error);\n            setIsStreaming(false);\n            setStreamingMessage('');\n            if (streamConnectionRef.current) {\n                streamConnectionRef.current.close();\n            }\n            // Fallback: try to reload messages to get the AI response\n            console.log('Attempting to reload messages as fallback...');\n            try {\n                await loadMessages();\n            } catch (reloadError) {\n                console.error('Failed to reload messages:', reloadError);\n                alert('Failed to receive AI response. Please try again.');\n            }\n        };\n        try {\n            // Create new stream connection\n            const connection = await _services_chat__WEBPACK_IMPORTED_MODULE_2__.chatService.createStreamConnection(chat.id, onMessage, onError);\n            streamConnectionRef.current = connection;\n            // Set timeout for streaming (30 seconds)\n            setTimeout(()=>{\n                if (isStreaming) {\n                    console.log('Stream timeout, falling back to message reload');\n                    onError(new Error('Stream timeout'));\n                }\n            }, 30000);\n        } catch (error) {\n            console.error('Failed to create stream connection:', error);\n            setIsStreaming(false);\n            alert('Failed to establish connection for AI response. Please try again.');\n        }\n    };\n    const formatTime = (dateString)=>{\n        try {\n            return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_8__.formatDistanceToNow)(new Date(dateString), {\n                addSuffix: true\n            });\n        } catch (e) {\n            return '';\n        }\n    };\n    const handleKeyPress = (e)=>{\n        if (e.key === 'Enter' && !e.shiftKey) {\n            e.preventDefault();\n            handleSendMessage();\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col h-full overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 border-b p-4 animate-pulse\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-10 h-10 bg-muted rounded-full\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 289,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-muted rounded w-32\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-muted rounded w-20\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 292,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 288,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 p-4 space-y-4 overflow-y-auto\",\n                    children: Array.from({\n                        length: 3\n                    }).map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-3 animate-pulse\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-8 h-8 bg-muted rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 space-y-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-3/4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 301,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-muted rounded w-1/2\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 302,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, i, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 298,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n            lineNumber: 286,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-b p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                            className: \"w-12 h-12 ring-2 ring-[#2DD4BF]/20\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                                    src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                                    alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 319,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                                    className: \"bg-[#2DD4BF]/10 text-[#2DD4BF] font-semibold\",\n                                                    children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 320,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute bottom-0 right-0 w-3 h-3 bg-green-500 border-2 border-background rounded-full\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-semibold text-lg\",\n                                            children: ((_chat_character3 = chat.character) === null || _chat_character3 === void 0 ? void 0 : _chat_character3.name) || 'Unknown Character'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 text-sm text-muted-foreground\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        chat.messageCount,\n                                                        \" messages\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"•\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 331,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-green-500\",\n                                                    children: \"Online\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 327,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 316,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"hover:bg-muted/50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 314,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto overflow-x-hidden p-4 space-y-4 bg-gradient-to-b from-background to-muted/20\",\n                children: [\n                    messages.map((message, index)=>{\n                        var _chat_character, _chat_character1, _chat_character_name_charAt, _chat_character_name, _chat_character2;\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-end gap-2 \".concat(message.role === 'user' ? 'justify-end' : 'justify-start'),\n                            children: [\n                                message.role === 'assistant' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                    className: \"w-8 h-8 mb-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                            src: (_chat_character = chat.character) === null || _chat_character === void 0 ? void 0 : _chat_character.image,\n                                            alt: (_chat_character1 = chat.character) === null || _chat_character1 === void 0 ? void 0 : _chat_character1.name\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                            className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                            children: ((_chat_character2 = chat.character) === null || _chat_character2 === void 0 ? void 0 : (_chat_character_name = _chat_character2.name) === null || _chat_character_name === void 0 ? void 0 : (_chat_character_name_charAt = _chat_character_name.charAt(0)) === null || _chat_character_name_charAt === void 0 ? void 0 : _chat_character_name_charAt.toUpperCase()) || 'C'\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"max-w-[70%] rounded-2xl p-4 shadow-sm \".concat(message.role === 'user' ? 'bg-[#2DD4BF] text-white rounded-br-md' : 'bg-white dark:bg-muted border rounded-bl-md'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                            content: message.content,\n                                            role: message.role\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between mt-2 \".concat(message.role === 'user' ? 'text-white/70' : 'text-muted-foreground'),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs\",\n                                                    children: formatTime(message.createdAt)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 17\n                                                }, this),\n                                                message.role === 'user' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-1 h-1 bg-white/70 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 361,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, message.id, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this);\n                    }),\n                    isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end gap-2 justify-start\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.Avatar, {\n                                className: \"w-8 h-8 mb-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarImage, {\n                                        src: (_chat_character4 = chat.character) === null || _chat_character4 === void 0 ? void 0 : _chat_character4.image,\n                                        alt: (_chat_character5 = chat.character) === null || _chat_character5 === void 0 ? void 0 : _chat_character5.name\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_5__.AvatarFallback, {\n                                        className: \"text-xs bg-[#2DD4BF]/10 text-[#2DD4BF]\",\n                                        children: ((_chat_character6 = chat.character) === null || _chat_character6 === void 0 ? void 0 : (_chat_character_name1 = _chat_character6.name) === null || _chat_character_name1 === void 0 ? void 0 : (_chat_character_name_charAt1 = _chat_character_name1.charAt(0)) === null || _chat_character_name_charAt1 === void 0 ? void 0 : _chat_character_name_charAt1.toUpperCase()) || 'C'\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"max-w-[70%] rounded-2xl rounded-bl-md p-4 bg-white dark:bg-muted border shadow-sm\",\n                                children: [\n                                    streamingMessage ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MessageContent, {\n                                        content: streamingMessage,\n                                        role: \"assistant\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-muted-foreground\",\n                                                children: \"Thinking\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 401,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex space-x-1 ml-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.1s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 404,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-2 h-2 bg-[#2DD4BF] rounded-full animate-bounce\",\n                                                        style: {\n                                                            animationDelay: '0.2s'\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                        lineNumber: 405,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                lineNumber: 402,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 400,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mt-2 text-muted-foreground\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-1 h-1 bg-[#2DD4BF] rounded-full animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs\",\n                                                    children: \"Typing...\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                                    lineNumber: 413,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 410,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 420,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 345,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-shrink-0 border-t p-4 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-end space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        value: newMessage,\n                                        onChange: (e)=>setNewMessage(e.target.value),\n                                        onKeyPress: handleKeyPress,\n                                        placeholder: \"Message \".concat(((_chat_character7 = chat.character) === null || _chat_character7 === void 0 ? void 0 : _chat_character7.name) || 'character', \"...\"),\n                                        disabled: sending || isStreaming,\n                                        className: \"pr-12 py-3 rounded-2xl border-2 focus:border-[#2DD4BF] transition-colors\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute right-3 top-1/2 transform -translate-y-1/2\",\n                                        children: (sending || isStreaming) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-4 h-4 border-2 border-[#2DD4BF] border-t-transparent rounded-full animate-spin\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                onClick: handleSendMessage,\n                                disabled: !newMessage.trim() || sending || isStreaming,\n                                className: \"bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-2xl px-6 py-3 transition-all duration-200 hover:scale-105 disabled:hover:scale-100\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MoreVertical_Send_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                    lineNumber: 448,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mt-2 text-xs text-muted-foreground\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: \"Press Enter to send, Shift+Enter for new line\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 454,\n                                columnNumber: 11\n                            }, this),\n                            isStreaming && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-[#2DD4BF] animate-pulse\",\n                                children: \"AI is responding...\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                        lineNumber: 453,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\components\\\\chat\\\\chat-interface.tsx\",\n        lineNumber: 312,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatInterface, \"HWCezY2ns3dsLvFKUIeDCj1R1D4=\");\n_c1 = ChatInterface;\nvar _c, _c1;\n$RefreshReg$(_c, \"MessageContent\");\n$RefreshReg$(_c1, \"ChatInterface\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/chat/chat-interface.tsx\n"));

/***/ })

});
"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/chat/page",{

/***/ "(app-pages-browser)/./src/app/chat/page.tsx":
/*!*******************************!*\
  !*** ./src/app/chat/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChatPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/chat/chat-list */ \"(app-pages-browser)/./src/components/chat/chat-list.tsx\");\n/* harmony import */ var _components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/chat/chat-interface */ \"(app-pages-browser)/./src/components/chat/chat-interface.tsx\");\n/* harmony import */ var _components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/chat/character-profile-sidebar */ \"(app-pages-browser)/./src/components/chat/character-profile-sidebar.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/auth/auth-modal */ \"(app-pages-browser)/./src/components/auth/auth-modal.tsx\");\n/* harmony import */ var _components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/app-sidebar */ \"(app-pages-browser)/./src/components/app-sidebar.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./src/components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_separator__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/separator */ \"(app-pages-browser)/./src/components/ui/separator.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=MessageCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-circle.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ChatPageContent() {\n    _s();\n    const { isAuthenticated, isLoading } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth)();\n    const [selectedChat, setSelectedChat] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isProfileOpen, setIsProfileOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showAuthModal, setShowAuthModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isChatListOpen, setIsChatListOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMobile, setIsMobile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const chatId = searchParams.get('id');\n    // Detect mobile screen size\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            const checkMobile = {\n                \"ChatPageContent.useEffect.checkMobile\": ()=>{\n                    setIsMobile(window.innerWidth < 768);\n                }\n            }[\"ChatPageContent.useEffect.checkMobile\"];\n            checkMobile();\n            window.addEventListener('resize', checkMobile);\n            return ({\n                \"ChatPageContent.useEffect\": ()=>window.removeEventListener('resize', checkMobile)\n            })[\"ChatPageContent.useEffect\"];\n        }\n    }[\"ChatPageContent.useEffect\"], []);\n    // Mobile behavior: close chat list when chat is selected, close profile by default\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (isMobile) {\n                if (selectedChat) {\n                    setIsChatListOpen(false);\n                }\n                setIsProfileOpen(false);\n            } else {\n                setIsChatListOpen(true);\n                setIsProfileOpen(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isMobile,\n        selectedChat\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ChatPageContent.useEffect\": ()=>{\n            if (!isLoading && !isAuthenticated) {\n                // Show auth modal instead of redirecting\n                setShowAuthModal(true);\n            }\n        }\n    }[\"ChatPageContent.useEffect\"], [\n        isAuthenticated,\n        isLoading\n    ]);\n    // Handle chats loaded callback\n    const handleChatsLoaded = (chats)=>{\n        console.log('Chats loaded:', chats);\n        // Auto-select chat from URL if available\n        if (chatId && !selectedChat) {\n            const targetChat = chats.find((chat)=>chat.id === chatId);\n            if (targetChat) {\n                console.log('Auto-selecting chat from URL:', targetChat);\n                setSelectedChat(targetChat);\n            } else {\n                console.log('Chat not found in loaded chats:', chatId);\n                // Remove invalid chat ID from URL\n                const newUrl = new URL(window.location.href);\n                newUrl.searchParams.delete('id');\n                window.history.replaceState({}, '', newUrl.toString());\n            }\n        }\n    };\n    const handleChatSelect = (chat)=>{\n        setSelectedChat(chat);\n        // Update URL with chat ID\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.set('id', chat.id);\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, close chat list when chat is selected\n        if (isMobile) {\n            setIsChatListOpen(false);\n        }\n    };\n    const handleBackToList = ()=>{\n        setSelectedChat(null);\n        // Remove chat ID from URL\n        const newUrl = new URL(window.location.href);\n        newUrl.searchParams.delete('id');\n        window.history.pushState({}, '', newUrl.toString());\n        // On mobile, show chat list when going back\n        if (isMobile) {\n            setIsChatListOpen(true);\n        }\n    };\n    // Toggle chat list (for mobile)\n    const toggleChatList = ()=>{\n        setIsChatListOpen(!isChatListOpen);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 125,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 123,\n            columnNumber: 7\n        }, this);\n    }\n    if (!isAuthenticated) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                    className: \"flex flex-col h-screen overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                            className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 px-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                        className: \"-ml-1\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 139,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                        orientation: \"vertical\",\n                                        className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    className: \"hidden md:block\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                        href: \"/dashboard\",\n                                                        children: \"Bestieku\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 147,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 146,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                    className: \"hidden md:block\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                        children: \"Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 153,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-1 min-h-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center max-w-md mx-auto p-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 164,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold mb-2\",\n                                            children: \"Selamat Datang di Bestieku Chat\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-muted-foreground mb-6\",\n                                            children: \"Untuk mulai mengobrol dengan karakter AI favorit Anda, silakan masuk atau daftar terlebih dahulu.\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowAuthModal(true),\n                                            className: \"inline-flex items-center px-6 py-3 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors font-medium\",\n                                            children: \"Masuk / Daftar\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_auth_auth_modal__WEBPACK_IMPORTED_MODULE_7__.AuthModal, {\n                    isOpen: showAuthModal,\n                    onClose: ()=>setShowAuthModal(false)\n                }, void 0, false, {\n                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                    lineNumber: 181,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 134,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarProvider, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_app_sidebar__WEBPACK_IMPORTED_MODULE_8__.AppSidebar, {}, void 0, false, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 191,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarInset, {\n                className: \"flex flex-col h-screen overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                        className: \"flex h-16 shrink-0 items-center gap-2 border-b\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 px-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_11__.SidebarTrigger, {\n                                    className: \"-ml-1\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_separator__WEBPACK_IMPORTED_MODULE_10__.Separator, {\n                                    orientation: \"vertical\",\n                                    className: \"mr-2 data-[orientation=vertical]:h-4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.Breadcrumb, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbList, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                className: \"hidden md:block\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbLink, {\n                                                    href: \"/dashboard\",\n                                                    children: \"Bestieku\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 203,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbSeparator, {\n                                                className: \"hidden md:block\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 207,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbItem, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_9__.BreadcrumbPage, {\n                                                    children: \"Chat\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-1 min-h-0\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-80 border-r bg-background flex flex-col min-h-0\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_list__WEBPACK_IMPORTED_MODULE_3__.ChatList, {\n                                    selectedChatId: selectedChat === null || selectedChat === void 0 ? void 0 : selectedChat.id,\n                                    onChatSelect: handleChatSelect,\n                                    onChatsLoaded: handleChatsLoaded\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex-1 flex min-h-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 flex flex-col min-h-0\",\n                                        children: selectedChat ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_chat_interface__WEBPACK_IMPORTED_MODULE_4__.ChatInterface, {\n                                            chat: selectedChat,\n                                            onBack: handleBackToList\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1 flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center max-w-md mx-auto p-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_MessageCircle_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-16 h-16 mx-auto mb-6 text-muted-foreground/50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                        className: \"text-xl font-semibold mb-2\",\n                                                        children: \"Welcome to Bestieku Chat\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-muted-foreground mb-6\",\n                                                        children: \"Select a chat from the sidebar to start messaging with your AI characters, or go back to the dashboard to start a new conversation.\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>router.push('/dashboard'),\n                                                        className: \"inline-flex items-center px-4 py-2 bg-[#2DD4BF] hover:bg-[#14B8A6] text-white rounded-lg transition-colors\",\n                                                        children: \"Browse Characters\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 236,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 13\n                                    }, this),\n                                    selectedChat && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 min-h-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_character_profile_sidebar__WEBPACK_IMPORTED_MODULE_5__.CharacterProfileSidebar, {\n                                            characterId: selectedChat.characterId,\n                                            messageCount: selectedChat.messageCount,\n                                            isOpen: isProfileOpen,\n                                            onToggle: ()=>setIsProfileOpen(!isProfileOpen)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                            lineNumber: 258,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                        lineNumber: 257,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 190,\n        columnNumber: 5\n    }, this);\n}\n_s(ChatPageContent, \"/cpINkfDV5S577WC7EARMNrbbcc=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_6__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = ChatPageContent;\nfunction ChatPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-[#2DD4BF] mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 278,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-muted-foreground\",\n                        children: \"Loading chat...\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                        lineNumber: 279,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n                lineNumber: 277,\n                columnNumber: 9\n            }, void 0)\n        }, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 276,\n            columnNumber: 7\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChatPageContent, {}, void 0, false, {\n            fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n            lineNumber: 283,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\besstieku\\\\frontend\\\\src\\\\app\\\\chat\\\\page.tsx\",\n        lineNumber: 275,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ChatPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ChatPageContent\");\n$RefreshReg$(_c1, \"ChatPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY2hhdC9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUVzRDtBQUNPO0FBRU47QUFDVTtBQUNxQjtBQUNwQztBQUNPO0FBQ0g7QUFRbEI7QUFDa0I7QUFLckI7QUFDWTtBQUU3QyxTQUFTc0I7O0lBQ1AsTUFBTSxFQUFFQyxlQUFlLEVBQUVDLFNBQVMsRUFBRSxHQUFHaEIsK0RBQU9BO0lBQzlDLE1BQU0sQ0FBQ2lCLGNBQWNDLGdCQUFnQixHQUFHMUIsK0NBQVFBLENBQWM7SUFDOUQsTUFBTSxDQUFDMkIsZUFBZUMsaUJBQWlCLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUNuRCxNQUFNLENBQUM2QixlQUFlQyxpQkFBaUIsR0FBRzlCLCtDQUFRQSxDQUFDO0lBQ25ELE1BQU0sQ0FBQytCLGdCQUFnQkMsa0JBQWtCLEdBQUdoQywrQ0FBUUEsQ0FBQztJQUNyRCxNQUFNLENBQUNpQyxVQUFVQyxZQUFZLEdBQUdsQywrQ0FBUUEsQ0FBQztJQUN6QyxNQUFNbUMsZUFBZWhDLGdFQUFlQTtJQUNwQyxNQUFNaUMsU0FBU2hDLDBEQUFTQTtJQUN4QixNQUFNaUMsU0FBU0YsYUFBYUcsR0FBRyxDQUFDO0lBRWhDLDRCQUE0QjtJQUM1QnJDLGdEQUFTQTtxQ0FBQztZQUNSLE1BQU1zQzt5REFBYztvQkFDbEJMLFlBQVlNLE9BQU9DLFVBQVUsR0FBRztnQkFDbEM7O1lBRUFGO1lBQ0FDLE9BQU9FLGdCQUFnQixDQUFDLFVBQVVIO1lBQ2xDOzZDQUFPLElBQU1DLE9BQU9HLG1CQUFtQixDQUFDLFVBQVVKOztRQUNwRDtvQ0FBRyxFQUFFO0lBRUwsbUZBQW1GO0lBQ25GdEMsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSWdDLFVBQVU7Z0JBQ1osSUFBSVIsY0FBYztvQkFDaEJPLGtCQUFrQjtnQkFDcEI7Z0JBQ0FKLGlCQUFpQjtZQUNuQixPQUFPO2dCQUNMSSxrQkFBa0I7Z0JBQ2xCSixpQkFBaUI7WUFDbkI7UUFDRjtvQ0FBRztRQUFDSztRQUFVUjtLQUFhO0lBRTNCeEIsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSSxDQUFDdUIsYUFBYSxDQUFDRCxpQkFBaUI7Z0JBQ2xDLHlDQUF5QztnQkFDekNPLGlCQUFpQjtZQUNuQjtRQUNGO29DQUFHO1FBQUNQO1FBQWlCQztLQUFVO0lBRS9CLCtCQUErQjtJQUMvQixNQUFNb0Isb0JBQW9CLENBQUNDO1FBQ3pCQyxRQUFRQyxHQUFHLENBQUMsaUJBQWlCRjtRQUU3Qix5Q0FBeUM7UUFDekMsSUFBSVIsVUFBVSxDQUFDWixjQUFjO1lBQzNCLE1BQU11QixhQUFhSCxNQUFNSSxJQUFJLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLEVBQUUsS0FBS2Q7WUFDbEQsSUFBSVcsWUFBWTtnQkFDZEYsUUFBUUMsR0FBRyxDQUFDLGlDQUFpQ0M7Z0JBQzdDdEIsZ0JBQWdCc0I7WUFDbEIsT0FBTztnQkFDTEYsUUFBUUMsR0FBRyxDQUFDLG1DQUFtQ1Y7Z0JBQy9DLGtDQUFrQztnQkFDbEMsTUFBTWUsU0FBUyxJQUFJQyxJQUFJYixPQUFPYyxRQUFRLENBQUNDLElBQUk7Z0JBQzNDSCxPQUFPakIsWUFBWSxDQUFDcUIsTUFBTSxDQUFDO2dCQUMzQmhCLE9BQU9pQixPQUFPLENBQUNDLFlBQVksQ0FBQyxDQUFDLEdBQUcsSUFBSU4sT0FBT08sUUFBUTtZQUNyRDtRQUNGO0lBQ0Y7SUFFQSxNQUFNQyxtQkFBbUIsQ0FBQ1Y7UUFDeEJ4QixnQkFBZ0J3QjtRQUNoQiwwQkFBMEI7UUFDMUIsTUFBTUUsU0FBUyxJQUFJQyxJQUFJYixPQUFPYyxRQUFRLENBQUNDLElBQUk7UUFDM0NILE9BQU9qQixZQUFZLENBQUMwQixHQUFHLENBQUMsTUFBTVgsS0FBS0MsRUFBRTtRQUNyQ1gsT0FBT2lCLE9BQU8sQ0FBQ0ssU0FBUyxDQUFDLENBQUMsR0FBRyxJQUFJVixPQUFPTyxRQUFRO1FBRWhELG1EQUFtRDtRQUNuRCxJQUFJMUIsVUFBVTtZQUNaRCxrQkFBa0I7UUFDcEI7SUFDRjtJQUVBLE1BQU0rQixtQkFBbUI7UUFDdkJyQyxnQkFBZ0I7UUFDaEIsMEJBQTBCO1FBQzFCLE1BQU0wQixTQUFTLElBQUlDLElBQUliLE9BQU9jLFFBQVEsQ0FBQ0MsSUFBSTtRQUMzQ0gsT0FBT2pCLFlBQVksQ0FBQ3FCLE1BQU0sQ0FBQztRQUMzQmhCLE9BQU9pQixPQUFPLENBQUNLLFNBQVMsQ0FBQyxDQUFDLEdBQUcsSUFBSVYsT0FBT08sUUFBUTtRQUVoRCw0Q0FBNEM7UUFDNUMsSUFBSTFCLFVBQVU7WUFDWkQsa0JBQWtCO1FBQ3BCO0lBQ0Y7SUFFQSxnQ0FBZ0M7SUFDaEMsTUFBTWdDLGlCQUFpQjtRQUNyQmhDLGtCQUFrQixDQUFDRDtJQUNyQjtJQUVBLElBQUlQLFdBQVc7UUFDYixxQkFDRSw4REFBQ3lDO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7Ozs7OztrQ0FDZiw4REFBQ0M7d0JBQUVELFdBQVU7a0NBQXdCOzs7Ozs7Ozs7Ozs7Ozs7OztJQUk3QztJQUVBLElBQUksQ0FBQzNDLGlCQUFpQjtRQUNwQixxQkFDRSw4REFBQ0osb0VBQWVBOzs4QkFDZCw4REFBQ1QsK0RBQVVBOzs7Ozs4QkFDWCw4REFBQ1EsaUVBQVlBO29CQUFDZ0QsV0FBVTs7c0NBQ3RCLDhEQUFDRTs0QkFBT0YsV0FBVTtzQ0FDaEIsNEVBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQzlDLG1FQUFjQTt3Q0FBQzhDLFdBQVU7Ozs7OztrREFDMUIsOERBQUNqRCxnRUFBU0E7d0NBQ1JvRCxhQUFZO3dDQUNaSCxXQUFVOzs7Ozs7a0RBRVosOERBQUN2RCxpRUFBVUE7a0RBQ1QsNEVBQUNHLHFFQUFjQTs7OERBQ2IsOERBQUNGLHFFQUFjQTtvREFBQ3NELFdBQVU7OERBQ3hCLDRFQUFDckQscUVBQWNBO3dEQUFDMEMsTUFBSztrRUFBYTs7Ozs7Ozs7Ozs7OERBSXBDLDhEQUFDdkMsMEVBQW1CQTtvREFBQ2tELFdBQVU7Ozs7Ozs4REFDL0IsOERBQUN0RCxxRUFBY0E7OERBQ2IsNEVBQUNHLHFFQUFjQTtrRUFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQVExQiw4REFBQ2tEOzRCQUFJQyxXQUFVO3NDQUNiLDRFQUFDRDtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDN0MsMEZBQWFBOzRDQUFDNkMsV0FBVTs7Ozs7O3NEQUN6Qiw4REFBQ0k7NENBQUdKLFdBQVU7c0RBQTZCOzs7Ozs7c0RBQzNDLDhEQUFDQzs0Q0FBRUQsV0FBVTtzREFBNkI7Ozs7OztzREFHMUMsOERBQUNLOzRDQUNDQyxTQUFTLElBQU0xQyxpQkFBaUI7NENBQ2hDb0MsV0FBVTtzREFDWDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFTVCw4REFBQ3pELGtFQUFTQTtvQkFDUmdFLFFBQVE1QztvQkFDUjZDLFNBQVMsSUFBTTVDLGlCQUFpQjs7Ozs7Ozs7Ozs7O0lBSXhDO0lBRUEscUJBQ0UsOERBQUNYLG9FQUFlQTs7MEJBQ2QsOERBQUNULCtEQUFVQTs7Ozs7MEJBQ1gsOERBQUNRLGlFQUFZQTtnQkFBQ2dELFdBQVU7O2tDQUN0Qiw4REFBQ0U7d0JBQU9GLFdBQVU7a0NBQ2hCLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUM5QyxtRUFBY0E7b0NBQUM4QyxXQUFVOzs7Ozs7OENBQzFCLDhEQUFDakQsZ0VBQVNBO29DQUNSb0QsYUFBWTtvQ0FDWkgsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDdkQsaUVBQVVBOzhDQUNULDRFQUFDRyxxRUFBY0E7OzBEQUNiLDhEQUFDRixxRUFBY0E7Z0RBQUNzRCxXQUFVOzBEQUN4Qiw0RUFBQ3JELHFFQUFjQTtvREFBQzBDLE1BQUs7OERBQWE7Ozs7Ozs7Ozs7OzBEQUlwQyw4REFBQ3ZDLDBFQUFtQkE7Z0RBQUNrRCxXQUFVOzs7Ozs7MERBQy9CLDhEQUFDdEQscUVBQWNBOzBEQUNiLDRFQUFDRyxxRUFBY0E7OERBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FRMUIsOERBQUNrRDt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOzBDQUNiLDRFQUFDN0QsZ0VBQVFBO29DQUNQc0UsY0FBYyxFQUFFbEQseUJBQUFBLG1DQUFBQSxhQUFjMEIsRUFBRTtvQ0FDaEN5QixjQUFjaEI7b0NBQ2RpQixlQUFlakM7Ozs7Ozs7Ozs7OzBDQUtuQiw4REFBQ3FCO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7d0NBQUlDLFdBQVU7a0RBQ1p6Qyw2QkFDQyw4REFBQ25CLDBFQUFhQTs0Q0FDWjRDLE1BQU16Qjs0Q0FDTnFELFFBQVFmOzs7OztpRUFHViw4REFBQ0U7NENBQUlDLFdBQVU7c0RBQ2IsNEVBQUNEO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQzdDLDBGQUFhQTt3REFBQzZDLFdBQVU7Ozs7OztrRUFDekIsOERBQUNJO3dEQUFHSixXQUFVO2tFQUE2Qjs7Ozs7O2tFQUMzQyw4REFBQ0M7d0RBQUVELFdBQVU7a0VBQTZCOzs7Ozs7a0VBSTFDLDhEQUFDSzt3REFDQ0MsU0FBUyxJQUFNcEMsT0FBTzJDLElBQUksQ0FBQzt3REFDM0JiLFdBQVU7a0VBQ1g7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7b0NBU1J6Qyw4QkFDQyw4REFBQ3dDO3dDQUFJQyxXQUFVO2tEQUNiLDRFQUFDM0QsK0ZBQXVCQTs0Q0FDdEJ5RSxhQUFhdkQsYUFBYXVELFdBQVc7NENBQ3JDQyxjQUFjeEQsYUFBYXdELFlBQVk7NENBQ3ZDUixRQUFROUM7NENBQ1J1RCxVQUFVLElBQU10RCxpQkFBaUIsQ0FBQ0Q7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBU3BEO0dBblBTTDs7UUFDZ0NkLDJEQUFPQTtRQU16QkwsNERBQWVBO1FBQ3JCQyxzREFBU0E7OztLQVJqQmtCO0FBcVBNLFNBQVM2RDtJQUN0QixxQkFDRSw4REFBQ2pGLDJDQUFRQTtRQUFDa0Ysd0JBQ1IsOERBQUNuQjtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzs7Ozs7a0NBQ2YsOERBQUNDO3dCQUFFRCxXQUFVO2tDQUF3Qjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0JBSXpDLDRFQUFDNUM7Ozs7Ozs7Ozs7QUFHUDtNQWJ3QjZEIiwic291cmNlcyI6WyJFOlxcYmVzc3RpZWt1XFxmcm9udGVuZFxcc3JjXFxhcHBcXGNoYXRcXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIFN1c3BlbnNlIH0gZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyB1c2VTZWFyY2hQYXJhbXMsIHVzZVJvdXRlciB9IGZyb20gJ25leHQvbmF2aWdhdGlvbic7XHJcbmltcG9ydCB7IENoYXQgfSBmcm9tICdAL3R5cGVzL2NoYXQnO1xyXG5pbXBvcnQgeyBDaGF0TGlzdCB9IGZyb20gJ0AvY29tcG9uZW50cy9jaGF0L2NoYXQtbGlzdCc7XHJcbmltcG9ydCB7IENoYXRJbnRlcmZhY2UgfSBmcm9tICdAL2NvbXBvbmVudHMvY2hhdC9jaGF0LWludGVyZmFjZSc7XHJcbmltcG9ydCB7IENoYXJhY3RlclByb2ZpbGVTaWRlYmFyIH0gZnJvbSAnQC9jb21wb25lbnRzL2NoYXQvY2hhcmFjdGVyLXByb2ZpbGUtc2lkZWJhcic7XHJcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2NvbnRleHRzL2F1dGgtY29udGV4dCc7XHJcbmltcG9ydCB7IEF1dGhNb2RhbCB9IGZyb20gJ0AvY29tcG9uZW50cy9hdXRoL2F1dGgtbW9kYWwnO1xyXG5pbXBvcnQgeyBBcHBTaWRlYmFyIH0gZnJvbSBcIkAvY29tcG9uZW50cy9hcHAtc2lkZWJhclwiO1xyXG5pbXBvcnQge1xyXG4gIEJyZWFkY3J1bWIsXHJcbiAgQnJlYWRjcnVtYkl0ZW0sXHJcbiAgQnJlYWRjcnVtYkxpbmssXHJcbiAgQnJlYWRjcnVtYkxpc3QsXHJcbiAgQnJlYWRjcnVtYlBhZ2UsXHJcbiAgQnJlYWRjcnVtYlNlcGFyYXRvcixcclxufSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JyZWFkY3J1bWJcIjtcclxuaW1wb3J0IHsgU2VwYXJhdG9yIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9zZXBhcmF0b3JcIjtcclxuaW1wb3J0IHtcclxuICBTaWRlYmFySW5zZXQsXHJcbiAgU2lkZWJhclByb3ZpZGVyLFxyXG4gIFNpZGViYXJUcmlnZ2VyLFxyXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvc2lkZWJhclwiO1xyXG5pbXBvcnQgeyBNZXNzYWdlQ2lyY2xlIH0gZnJvbSAnbHVjaWRlLXJlYWN0JztcclxuXHJcbmZ1bmN0aW9uIENoYXRQYWdlQ29udGVudCgpIHtcclxuICBjb25zdCB7IGlzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nIH0gPSB1c2VBdXRoKCk7XHJcbiAgY29uc3QgW3NlbGVjdGVkQ2hhdCwgc2V0U2VsZWN0ZWRDaGF0XSA9IHVzZVN0YXRlPENoYXQgfCBudWxsPihudWxsKTtcclxuICBjb25zdCBbaXNQcm9maWxlT3Blbiwgc2V0SXNQcm9maWxlT3Blbl0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbc2hvd0F1dGhNb2RhbCwgc2V0U2hvd0F1dGhNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XHJcbiAgY29uc3QgW2lzQ2hhdExpc3RPcGVuLCBzZXRJc0NoYXRMaXN0T3Blbl0gPSB1c2VTdGF0ZSh0cnVlKTtcclxuICBjb25zdCBbaXNNb2JpbGUsIHNldElzTW9iaWxlXSA9IHVzZVN0YXRlKGZhbHNlKTtcclxuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcclxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKTtcclxuICBjb25zdCBjaGF0SWQgPSBzZWFyY2hQYXJhbXMuZ2V0KCdpZCcpO1xyXG5cclxuICAvLyBEZXRlY3QgbW9iaWxlIHNjcmVlbiBzaXplXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGNvbnN0IGNoZWNrTW9iaWxlID0gKCkgPT4ge1xyXG4gICAgICBzZXRJc01vYmlsZSh3aW5kb3cuaW5uZXJXaWR0aCA8IDc2OCk7XHJcbiAgICB9O1xyXG5cclxuICAgIGNoZWNrTW9iaWxlKCk7XHJcbiAgICB3aW5kb3cuYWRkRXZlbnRMaXN0ZW5lcigncmVzaXplJywgY2hlY2tNb2JpbGUpO1xyXG4gICAgcmV0dXJuICgpID0+IHdpbmRvdy5yZW1vdmVFdmVudExpc3RlbmVyKCdyZXNpemUnLCBjaGVja01vYmlsZSk7XHJcbiAgfSwgW10pO1xyXG5cclxuICAvLyBNb2JpbGUgYmVoYXZpb3I6IGNsb3NlIGNoYXQgbGlzdCB3aGVuIGNoYXQgaXMgc2VsZWN0ZWQsIGNsb3NlIHByb2ZpbGUgYnkgZGVmYXVsdFxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoaXNNb2JpbGUpIHtcclxuICAgICAgaWYgKHNlbGVjdGVkQ2hhdCkge1xyXG4gICAgICAgIHNldElzQ2hhdExpc3RPcGVuKGZhbHNlKTtcclxuICAgICAgfVxyXG4gICAgICBzZXRJc1Byb2ZpbGVPcGVuKGZhbHNlKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHNldElzQ2hhdExpc3RPcGVuKHRydWUpO1xyXG4gICAgICBzZXRJc1Byb2ZpbGVPcGVuKHRydWUpO1xyXG4gICAgfVxyXG4gIH0sIFtpc01vYmlsZSwgc2VsZWN0ZWRDaGF0XSk7XHJcblxyXG4gIHVzZUVmZmVjdCgoKSA9PiB7XHJcbiAgICBpZiAoIWlzTG9hZGluZyAmJiAhaXNBdXRoZW50aWNhdGVkKSB7XHJcbiAgICAgIC8vIFNob3cgYXV0aCBtb2RhbCBpbnN0ZWFkIG9mIHJlZGlyZWN0aW5nXHJcbiAgICAgIHNldFNob3dBdXRoTW9kYWwodHJ1ZSk7XHJcbiAgICB9XHJcbiAgfSwgW2lzQXV0aGVudGljYXRlZCwgaXNMb2FkaW5nXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBjaGF0cyBsb2FkZWQgY2FsbGJhY2tcclxuICBjb25zdCBoYW5kbGVDaGF0c0xvYWRlZCA9IChjaGF0czogQ2hhdFtdKSA9PiB7XHJcbiAgICBjb25zb2xlLmxvZygnQ2hhdHMgbG9hZGVkOicsIGNoYXRzKTtcclxuXHJcbiAgICAvLyBBdXRvLXNlbGVjdCBjaGF0IGZyb20gVVJMIGlmIGF2YWlsYWJsZVxyXG4gICAgaWYgKGNoYXRJZCAmJiAhc2VsZWN0ZWRDaGF0KSB7XHJcbiAgICAgIGNvbnN0IHRhcmdldENoYXQgPSBjaGF0cy5maW5kKGNoYXQgPT4gY2hhdC5pZCA9PT0gY2hhdElkKTtcclxuICAgICAgaWYgKHRhcmdldENoYXQpIHtcclxuICAgICAgICBjb25zb2xlLmxvZygnQXV0by1zZWxlY3RpbmcgY2hhdCBmcm9tIFVSTDonLCB0YXJnZXRDaGF0KTtcclxuICAgICAgICBzZXRTZWxlY3RlZENoYXQodGFyZ2V0Q2hhdCk7XHJcbiAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgY29uc29sZS5sb2coJ0NoYXQgbm90IGZvdW5kIGluIGxvYWRlZCBjaGF0czonLCBjaGF0SWQpO1xyXG4gICAgICAgIC8vIFJlbW92ZSBpbnZhbGlkIGNoYXQgSUQgZnJvbSBVUkxcclxuICAgICAgICBjb25zdCBuZXdVcmwgPSBuZXcgVVJMKHdpbmRvdy5sb2NhdGlvbi5ocmVmKTtcclxuICAgICAgICBuZXdVcmwuc2VhcmNoUGFyYW1zLmRlbGV0ZSgnaWQnKTtcclxuICAgICAgICB3aW5kb3cuaGlzdG9yeS5yZXBsYWNlU3RhdGUoe30sICcnLCBuZXdVcmwudG9TdHJpbmcoKSk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVDaGF0U2VsZWN0ID0gKGNoYXQ6IENoYXQpID0+IHtcclxuICAgIHNldFNlbGVjdGVkQ2hhdChjaGF0KTtcclxuICAgIC8vIFVwZGF0ZSBVUkwgd2l0aCBjaGF0IElEXHJcbiAgICBjb25zdCBuZXdVcmwgPSBuZXcgVVJMKHdpbmRvdy5sb2NhdGlvbi5ocmVmKTtcclxuICAgIG5ld1VybC5zZWFyY2hQYXJhbXMuc2V0KCdpZCcsIGNoYXQuaWQpO1xyXG4gICAgd2luZG93Lmhpc3RvcnkucHVzaFN0YXRlKHt9LCAnJywgbmV3VXJsLnRvU3RyaW5nKCkpO1xyXG5cclxuICAgIC8vIE9uIG1vYmlsZSwgY2xvc2UgY2hhdCBsaXN0IHdoZW4gY2hhdCBpcyBzZWxlY3RlZFxyXG4gICAgaWYgKGlzTW9iaWxlKSB7XHJcbiAgICAgIHNldElzQ2hhdExpc3RPcGVuKGZhbHNlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVCYWNrVG9MaXN0ID0gKCkgPT4ge1xyXG4gICAgc2V0U2VsZWN0ZWRDaGF0KG51bGwpO1xyXG4gICAgLy8gUmVtb3ZlIGNoYXQgSUQgZnJvbSBVUkxcclxuICAgIGNvbnN0IG5ld1VybCA9IG5ldyBVUkwod2luZG93LmxvY2F0aW9uLmhyZWYpO1xyXG4gICAgbmV3VXJsLnNlYXJjaFBhcmFtcy5kZWxldGUoJ2lkJyk7XHJcbiAgICB3aW5kb3cuaGlzdG9yeS5wdXNoU3RhdGUoe30sICcnLCBuZXdVcmwudG9TdHJpbmcoKSk7XHJcblxyXG4gICAgLy8gT24gbW9iaWxlLCBzaG93IGNoYXQgbGlzdCB3aGVuIGdvaW5nIGJhY2tcclxuICAgIGlmIChpc01vYmlsZSkge1xyXG4gICAgICBzZXRJc0NoYXRMaXN0T3Blbih0cnVlKTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICAvLyBUb2dnbGUgY2hhdCBsaXN0IChmb3IgbW9iaWxlKVxyXG4gIGNvbnN0IHRvZ2dsZUNoYXRMaXN0ID0gKCkgPT4ge1xyXG4gICAgc2V0SXNDaGF0TGlzdE9wZW4oIWlzQ2hhdExpc3RPcGVuKTtcclxuICB9O1xyXG5cclxuICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICByZXR1cm4gKFxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItWyMyREQ0QkZdIG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TG9hZGluZy4uLjwvcD5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApO1xyXG4gIH1cclxuXHJcbiAgaWYgKCFpc0F1dGhlbnRpY2F0ZWQpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxTaWRlYmFyUHJvdmlkZXI+XHJcbiAgICAgICAgPEFwcFNpZGViYXIgLz5cclxuICAgICAgICA8U2lkZWJhckluc2V0IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1zY3JlZW4gb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgICA8aGVhZGVyIGNsYXNzTmFtZT1cImZsZXggaC0xNiBzaHJpbmstMCBpdGVtcy1jZW50ZXIgZ2FwLTIgYm9yZGVyLWJcIj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBweC00XCI+XHJcbiAgICAgICAgICAgICAgPFNpZGViYXJUcmlnZ2VyIGNsYXNzTmFtZT1cIi1tbC0xXCIgLz5cclxuICAgICAgICAgICAgICA8U2VwYXJhdG9yXHJcbiAgICAgICAgICAgICAgICBvcmllbnRhdGlvbj1cInZlcnRpY2FsXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTIgZGF0YS1bb3JpZW50YXRpb249dmVydGljYWxdOmgtNFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8QnJlYWRjcnVtYj5cclxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iTGlzdD5cclxuICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtIGNsYXNzTmFtZT1cImhpZGRlbiBtZDpibG9ja1wiPlxyXG4gICAgICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iTGluayBocmVmPVwiL2Rhc2hib2FyZFwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgQmVzdGlla3VcclxuICAgICAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJMaW5rPlxyXG4gICAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYlNlcGFyYXRvciBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIiAvPlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJQYWdlPkNoYXQ8L0JyZWFkY3J1bWJQYWdlPlxyXG4gICAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iTGlzdD5cclxuICAgICAgICAgICAgICA8L0JyZWFkY3J1bWI+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9oZWFkZXI+XHJcblxyXG4gICAgICAgICAgey8qIE1haW4gQ2hhdCBBcmVhIC0gVW5hdXRoZW50aWNhdGVkICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtMSBtaW4taC0wXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBtYXgtdy1tZCBteC1hdXRvIHAtOFwiPlxyXG4gICAgICAgICAgICAgICAgPE1lc3NhZ2VDaXJjbGUgY2xhc3NOYW1lPVwidy0xNiBoLTE2IG14LWF1dG8gbWItNiB0ZXh0LW11dGVkLWZvcmVncm91bmQvNTBcIiAvPlxyXG4gICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yXCI+U2VsYW1hdCBEYXRhbmcgZGkgQmVzdGlla3UgQ2hhdDwvaDI+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICBVbnR1ayBtdWxhaSBtZW5nb2Jyb2wgZGVuZ2FuIGthcmFrdGVyIEFJIGZhdm9yaXQgQW5kYSwgc2lsYWthbiBtYXN1ayBhdGF1IGRhZnRhciB0ZXJsZWJpaCBkYWh1bHUuXHJcbiAgICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgICAgICA8YnV0dG9uXHJcbiAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHNldFNob3dBdXRoTW9kYWwodHJ1ZSl9XHJcbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC02IHB5LTMgYmctWyMyREQ0QkZdIGhvdmVyOmJnLVsjMTRCOEE2XSB0ZXh0LXdoaXRlIHJvdW5kZWQtbGcgdHJhbnNpdGlvbi1jb2xvcnMgZm9udC1tZWRpdW1cIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICBNYXN1ayAvIERhZnRhclxyXG4gICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9TaWRlYmFySW5zZXQ+XHJcblxyXG4gICAgICAgIHsvKiBBdXRoIE1vZGFsICovfVxyXG4gICAgICAgIDxBdXRoTW9kYWxcclxuICAgICAgICAgIGlzT3Blbj17c2hvd0F1dGhNb2RhbH1cclxuICAgICAgICAgIG9uQ2xvc2U9eygpID0+IHNldFNob3dBdXRoTW9kYWwoZmFsc2UpfVxyXG4gICAgICAgIC8+XHJcbiAgICAgIDwvU2lkZWJhclByb3ZpZGVyPlxyXG4gICAgKTtcclxuICB9XHJcblxyXG4gIHJldHVybiAoXHJcbiAgICA8U2lkZWJhclByb3ZpZGVyPlxyXG4gICAgICA8QXBwU2lkZWJhciAvPlxyXG4gICAgICA8U2lkZWJhckluc2V0IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgaC1zY3JlZW4gb3ZlcmZsb3ctaGlkZGVuXCI+XHJcbiAgICAgICAgPGhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGgtMTYgc2hyaW5rLTAgaXRlbXMtY2VudGVyIGdhcC0yIGJvcmRlci1iXCI+XHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHB4LTRcIj5cclxuICAgICAgICAgICAgPFNpZGViYXJUcmlnZ2VyIGNsYXNzTmFtZT1cIi1tbC0xXCIgLz5cclxuICAgICAgICAgICAgPFNlcGFyYXRvclxyXG4gICAgICAgICAgICAgIG9yaWVudGF0aW9uPVwidmVydGljYWxcIlxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm1yLTIgZGF0YS1bb3JpZW50YXRpb249dmVydGljYWxdOmgtNFwiXHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxCcmVhZGNydW1iPlxyXG4gICAgICAgICAgICAgIDxCcmVhZGNydW1iTGlzdD5cclxuICAgICAgICAgICAgICAgIDxCcmVhZGNydW1iSXRlbSBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIj5cclxuICAgICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJMaW5rIGhyZWY9XCIvZGFzaGJvYXJkXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgQmVzdGlla3VcclxuICAgICAgICAgICAgICAgICAgPC9CcmVhZGNydW1iTGluaz5cclxuICAgICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkl0ZW0+XHJcbiAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYlNlcGFyYXRvciBjbGFzc05hbWU9XCJoaWRkZW4gbWQ6YmxvY2tcIiAvPlxyXG4gICAgICAgICAgICAgICAgPEJyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICAgICAgICA8QnJlYWRjcnVtYlBhZ2U+Q2hhdDwvQnJlYWRjcnVtYlBhZ2U+XHJcbiAgICAgICAgICAgICAgICA8L0JyZWFkY3J1bWJJdGVtPlxyXG4gICAgICAgICAgICAgIDwvQnJlYWRjcnVtYkxpc3Q+XHJcbiAgICAgICAgICAgIDwvQnJlYWRjcnVtYj5cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvaGVhZGVyPlxyXG5cclxuICAgICAgICB7LyogTWFpbiBDaGF0IEFyZWEgLSBGaXhlZCBoZWlnaHQsIG5vIHNjcm9sbCAqL31cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC0xIG1pbi1oLTBcIj5cclxuICAgICAgICAgIHsvKiBDaGF0IExpc3QgU2lkZWJhciAtIEZpeGVkIHdpZHRoLCBvd24gc2Nyb2xsICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTgwIGJvcmRlci1yIGJnLWJhY2tncm91bmQgZmxleCBmbGV4LWNvbCBtaW4taC0wXCI+XHJcbiAgICAgICAgICAgIDxDaGF0TGlzdFxyXG4gICAgICAgICAgICAgIHNlbGVjdGVkQ2hhdElkPXtzZWxlY3RlZENoYXQ/LmlkfVxyXG4gICAgICAgICAgICAgIG9uQ2hhdFNlbGVjdD17aGFuZGxlQ2hhdFNlbGVjdH1cclxuICAgICAgICAgICAgICBvbkNoYXRzTG9hZGVkPXtoYW5kbGVDaGF0c0xvYWRlZH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgIHsvKiBDaGF0IEludGVyZmFjZSAtIEZsZXhpYmxlIHdpZHRoLCBvd24gc2Nyb2xsICovfVxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgZmxleCBtaW4taC0wXCI+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggZmxleC1jb2wgbWluLWgtMFwiPlxyXG4gICAgICAgICAgICAgIHtzZWxlY3RlZENoYXQgPyAoXHJcbiAgICAgICAgICAgICAgICA8Q2hhdEludGVyZmFjZVxyXG4gICAgICAgICAgICAgICAgICBjaGF0PXtzZWxlY3RlZENoYXR9XHJcbiAgICAgICAgICAgICAgICAgIG9uQmFjaz17aGFuZGxlQmFja1RvTGlzdH1cclxuICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgbWF4LXctbWQgbXgtYXV0byBwLThcIj5cclxuICAgICAgICAgICAgICAgICAgICA8TWVzc2FnZUNpcmNsZSBjbGFzc05hbWU9XCJ3LTE2IGgtMTYgbXgtYXV0byBtYi02IHRleHQtbXV0ZWQtZm9yZWdyb3VuZC81MFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQteGwgZm9udC1zZW1pYm9sZCBtYi0yXCI+V2VsY29tZSB0byBCZXN0aWVrdSBDaGF0PC9oMj5cclxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmQgbWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgU2VsZWN0IGEgY2hhdCBmcm9tIHRoZSBzaWRlYmFyIHRvIHN0YXJ0IG1lc3NhZ2luZyB3aXRoIHlvdXIgQUkgY2hhcmFjdGVycyxcclxuICAgICAgICAgICAgICAgICAgICAgIG9yIGdvIGJhY2sgdG8gdGhlIGRhc2hib2FyZCB0byBzdGFydCBhIG5ldyBjb252ZXJzYXRpb24uXHJcbiAgICAgICAgICAgICAgICAgICAgPC9wPlxyXG4gICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJyl9XHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIgcHgtNCBweS0yIGJnLVsjMkRENEJGXSBob3ZlcjpiZy1bIzE0QjhBNl0gdGV4dC13aGl0ZSByb3VuZGVkLWxnIHRyYW5zaXRpb24tY29sb3JzXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICBCcm93c2UgQ2hhcmFjdGVyc1xyXG4gICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICl9XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIENoYXJhY3RlciBQcm9maWxlIFNpZGViYXIgLSBGaXhlZC9mbGV4aWJsZSB3aWR0aCwgb3duIHNjcm9sbCAqL31cclxuICAgICAgICAgICAge3NlbGVjdGVkQ2hhdCAmJiAoXHJcbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wIG1pbi1oLTBcIj5cclxuICAgICAgICAgICAgICAgIDxDaGFyYWN0ZXJQcm9maWxlU2lkZWJhclxyXG4gICAgICAgICAgICAgICAgICBjaGFyYWN0ZXJJZD17c2VsZWN0ZWRDaGF0LmNoYXJhY3RlcklkfVxyXG4gICAgICAgICAgICAgICAgICBtZXNzYWdlQ291bnQ9e3NlbGVjdGVkQ2hhdC5tZXNzYWdlQ291bnR9XHJcbiAgICAgICAgICAgICAgICAgIGlzT3Blbj17aXNQcm9maWxlT3Blbn1cclxuICAgICAgICAgICAgICAgICAgb25Ub2dnbGU9eygpID0+IHNldElzUHJvZmlsZU9wZW4oIWlzUHJvZmlsZU9wZW4pfVxyXG4gICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L1NpZGViYXJJbnNldD5cclxuICAgIDwvU2lkZWJhclByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENoYXRQYWdlKCkge1xyXG4gIHJldHVybiAoXHJcbiAgICA8U3VzcGVuc2UgZmFsbGJhY2s9e1xyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImgtc2NyZWVuIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlclwiPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtOCB3LTggYm9yZGVyLWItMiBib3JkZXItWyMyREQ0QkZdIG14LWF1dG8gbWItNFwiPjwvZGl2PlxyXG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+TG9hZGluZyBjaGF0Li4uPC9wPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L2Rpdj5cclxuICAgIH0+XHJcbiAgICAgIDxDaGF0UGFnZUNvbnRlbnQgLz5cclxuICAgIDwvU3VzcGVuc2U+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJ1c2VFZmZlY3QiLCJTdXNwZW5zZSIsInVzZVNlYXJjaFBhcmFtcyIsInVzZVJvdXRlciIsIkNoYXRMaXN0IiwiQ2hhdEludGVyZmFjZSIsIkNoYXJhY3RlclByb2ZpbGVTaWRlYmFyIiwidXNlQXV0aCIsIkF1dGhNb2RhbCIsIkFwcFNpZGViYXIiLCJCcmVhZGNydW1iIiwiQnJlYWRjcnVtYkl0ZW0iLCJCcmVhZGNydW1iTGluayIsIkJyZWFkY3J1bWJMaXN0IiwiQnJlYWRjcnVtYlBhZ2UiLCJCcmVhZGNydW1iU2VwYXJhdG9yIiwiU2VwYXJhdG9yIiwiU2lkZWJhckluc2V0IiwiU2lkZWJhclByb3ZpZGVyIiwiU2lkZWJhclRyaWdnZXIiLCJNZXNzYWdlQ2lyY2xlIiwiQ2hhdFBhZ2VDb250ZW50IiwiaXNBdXRoZW50aWNhdGVkIiwiaXNMb2FkaW5nIiwic2VsZWN0ZWRDaGF0Iiwic2V0U2VsZWN0ZWRDaGF0IiwiaXNQcm9maWxlT3BlbiIsInNldElzUHJvZmlsZU9wZW4iLCJzaG93QXV0aE1vZGFsIiwic2V0U2hvd0F1dGhNb2RhbCIsImlzQ2hhdExpc3RPcGVuIiwic2V0SXNDaGF0TGlzdE9wZW4iLCJpc01vYmlsZSIsInNldElzTW9iaWxlIiwic2VhcmNoUGFyYW1zIiwicm91dGVyIiwiY2hhdElkIiwiZ2V0IiwiY2hlY2tNb2JpbGUiLCJ3aW5kb3ciLCJpbm5lcldpZHRoIiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVDaGF0c0xvYWRlZCIsImNoYXRzIiwiY29uc29sZSIsImxvZyIsInRhcmdldENoYXQiLCJmaW5kIiwiY2hhdCIsImlkIiwibmV3VXJsIiwiVVJMIiwibG9jYXRpb24iLCJocmVmIiwiZGVsZXRlIiwiaGlzdG9yeSIsInJlcGxhY2VTdGF0ZSIsInRvU3RyaW5nIiwiaGFuZGxlQ2hhdFNlbGVjdCIsInNldCIsInB1c2hTdGF0ZSIsImhhbmRsZUJhY2tUb0xpc3QiLCJ0b2dnbGVDaGF0TGlzdCIsImRpdiIsImNsYXNzTmFtZSIsInAiLCJoZWFkZXIiLCJvcmllbnRhdGlvbiIsImgyIiwiYnV0dG9uIiwib25DbGljayIsImlzT3BlbiIsIm9uQ2xvc2UiLCJzZWxlY3RlZENoYXRJZCIsIm9uQ2hhdFNlbGVjdCIsIm9uQ2hhdHNMb2FkZWQiLCJvbkJhY2siLCJwdXNoIiwiY2hhcmFjdGVySWQiLCJtZXNzYWdlQ291bnQiLCJvblRvZ2dsZSIsIkNoYXRQYWdlIiwiZmFsbGJhY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/chat/page.tsx\n"));

/***/ })

});